{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/styles/markdown.css"], "sourcesContent": ["/* Markdown 样式 */\n.markdown-content {\n  line-height: 1.6;\n  color: #24292e;\n}\n\n/* 行内代码样式 - 只对不在 pre 标签内的 code 应用 */\n.markdown-content p code,\n.markdown-content li code,\n.markdown-content h1 code,\n.markdown-content h2 code,\n.markdown-content h3 code,\n.markdown-content h4 code,\n.markdown-content h5 code,\n.markdown-content h6 code {\n  background-color: #f6f8fa !important;\n  padding: 2px 6px !important;\n  border-radius: 4px !important;\n  font-size: 0.9em !important;\n  font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, monospace !important;\n  border: 1px solid #e1e4e8 !important;\n  color: #24292e !important;\n  white-space: nowrap !important;\n  display: inline-block !important;\n  vertical-align: baseline !important;\n  word-break: keep-all !important;\n  overflow-wrap: normal !important;\n  max-width: none !important;\n  width: auto !important;\n}\n\n/* 代码块样式 */\n.markdown-content pre {\n  background-color: #f6f8fa !important;\n  padding: 16px !important;\n  border-radius: 6px !important;\n  overflow: auto !important;\n  font-size: 14px !important;\n  line-height: 1.45 !important;\n  margin: 16px 0 !important;\n  border: 1px solid #e1e4e8 !important;\n  font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, monospace !important;\n  white-space: pre !important;\n  color: #24292e !important;\n}\n\n/* 代码块内的 code 标签 */\n.markdown-content pre code {\n  background: none !important;\n  padding: 0 !important;\n  border: none !important;\n  border-radius: 0 !important;\n  font-size: inherit !important;\n  color: inherit !important;\n  white-space: pre !important;\n  display: block !important;\n  word-break: normal !important;\n  overflow-wrap: normal !important;\n}\n\n.markdown-content p {\n  margin: 0 0 12px 0 !important;\n  line-height: 1.6 !important;\n  color: #24292e !important;\n  font-size: 14px !important;\n}\n\n.markdown-content h1 {\n  font-size: 20px !important;\n  font-weight: 600 !important;\n  margin: 0 0 16px 0 !important;\n  color: #24292e !important;\n}\n\n.markdown-content h2 {\n  font-size: 18px !important;\n  font-weight: 600 !important;\n  margin: 0 0 12px 0 !important;\n  color: #24292e !important;\n}\n\n.markdown-content h3 {\n  font-size: 16px !important;\n  font-weight: 600 !important;\n  margin: 0 0 12px 0 !important;\n  color: #24292e !important;\n}\n\n.markdown-content ul,\n.markdown-content ol {\n  margin: 0 0 12px 0 !important;\n  padding-left: 20px !important;\n  color: #24292e !important;\n  font-size: 14px !important;\n}"], "names": [], "mappings": "AACA;;;;;AAMA;;;;;;;;;;;;;;;;;AAyBA;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA", "debugId": null}}]}