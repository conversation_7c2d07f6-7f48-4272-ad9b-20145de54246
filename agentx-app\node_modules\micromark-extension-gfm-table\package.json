{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/micromark/micromark-extension-gfm-table/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"devlop": "^1.0.0", "micromark-factory-space": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}, "description": "micromark extension to support GFM tables", "devDependencies": {"@types/node": "^22.0.0", "c8": "^10.0.0", "create-gfm-fixtures": "^2.0.0", "micromark": "^4.0.0", "micromark-build": "^2.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.60.0"}, "exports": {"development": "./dev/index.js", "default": "./index.js"}, "files": ["dev/", "index.d.ts", "index.js", "lib/"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["cell", "column", "gfm", "markdown", "micromark-extension", "micromark", "row", "table", "tabular", "unified"], "license": "MIT", "name": "micromark-extension-gfm-table", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "micromark/micromark-extension-gfm-table", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage && micromark-build", "format": "remark --frail --quiet --output -- . && prettier --log-level warn --write -- . && xo --fix", "test-api-dev": "node --conditions development test/index.js", "test-api-prod": "node --conditions production test/index.js", "test-api": "npm run test-api-dev && npm run test-api-prod", "test-coverage": "c8 --100 --reporter lcov -- npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "strict": true}, "type": "module", "version": "2.1.1", "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}, {"files": ["test/**/*.js"], "rules": {"no-await-in-loop": "off"}}], "prettier": true, "rules": {"complexity": "off", "max-depth": "off", "unicorn/no-this-assignment": "off", "unicorn/prefer-string-replace-all": "off"}}}