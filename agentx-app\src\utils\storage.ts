/**
 * Secure local storage utilities for AgentX
 * Handles encryption/decryption of sensitive data
 */

import { encryptValue, decryptValue, isEncryptionSupported } from './encryption';

// Storage keys
const STORAGE_KEYS = {
  API_KEY: 'agentx_api_key',
  API_BASE_URL: 'agentx_api_base_url',
  MODEL_NAME: 'agentx_model_name',
  CONVERSATIONS: 'agentx_conversations',
  SETTINGS: 'agentx_settings',
  USER_PREFERENCES: 'agentx_user_preferences',
} as const;

// Types
export interface ApiConfig {
  apiKey: string;
  baseUrl: string;
  modelName: string;
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
}

export interface AppSettings {
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  theme: 'light' | 'dark' | 'auto';
}

export interface UserPreferences {
  language: string;
  autoSave: boolean;
  showTimestamps: boolean;
}

// Default values
const DEFAULT_API_CONFIG: ApiConfig = {
  apiKey: '',
  baseUrl: 'https://c-z0-api-01.hash070.com/v1',
  modelName: 'gpt-4o-mini',
};

const DEFAULT_SETTINGS: AppSettings = {
  temperature: 0.7,
  maxTokens: 2048,
  systemPrompt: 'You are a helpful AI assistant.',
  theme: 'auto',
};

const DEFAULT_PREFERENCES: UserPreferences = {
  language: 'en',
  autoSave: true,
  showTimestamps: true,
};

/**
 * Secure storage for API configuration
 */
export class ApiConfigStorage {
  static async save(config: ApiConfig): Promise<void> {
    try {
      if (isEncryptionSupported() && config.apiKey) {
        const encryptedKey = await encryptValue(config.apiKey);
        localStorage.setItem(STORAGE_KEYS.API_KEY, encryptedKey);
      }

      localStorage.setItem(STORAGE_KEYS.API_BASE_URL, config.baseUrl);
      localStorage.setItem(STORAGE_KEYS.MODEL_NAME, config.modelName);
    } catch (error) {
      console.error('Failed to save API config:', error);
      throw new Error('Failed to save API configuration');
    }
  }

  static async load(): Promise<ApiConfig> {
    try {
      const baseUrl = localStorage.getItem(STORAGE_KEYS.API_BASE_URL) || DEFAULT_API_CONFIG.baseUrl;
      const modelName = localStorage.getItem(STORAGE_KEYS.MODEL_NAME) || DEFAULT_API_CONFIG.modelName;

      let apiKey = '';
      const encryptedKey = localStorage.getItem(STORAGE_KEYS.API_KEY);

      if (encryptedKey && isEncryptionSupported()) {
        try {
          apiKey = await decryptValue(encryptedKey);
        } catch (error) {
          console.warn('Failed to decrypt API key, using empty value');
          apiKey = '';
        }
      }

      return { apiKey, baseUrl, modelName };
    } catch (error) {
      console.error('Failed to load API config:', error);
      return DEFAULT_API_CONFIG;
    }
  }

  static clear(): void {
    localStorage.removeItem(STORAGE_KEYS.API_KEY);
    localStorage.removeItem(STORAGE_KEYS.API_BASE_URL);
    localStorage.removeItem(STORAGE_KEYS.MODEL_NAME);
  }
}

/**
 * Storage for conversations
 */
export class ConversationStorage {
  static save(conversations: Conversation[]): void {
    try {
      const data = JSON.stringify(conversations);
      localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, data);
    } catch (error) {
      console.error('Failed to save conversations:', error);
      throw new Error('Failed to save conversations');
    }
  }

  static load(): Conversation[] {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.CONVERSATIONS);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to load conversations:', error);
      return [];
    }
  }

  static addConversation(conversation: Conversation): void {
    const conversations = this.load();
    conversations.unshift(conversation); // Add to beginning
    this.save(conversations);
  }

  static updateConversation(conversationId: string, updates: Partial<Conversation>): void {
    const conversations = this.load();
    const index = conversations.findIndex(c => c.id === conversationId);

    if (index !== -1) {
      conversations[index] = { ...conversations[index], ...updates, updatedAt: Date.now() };
      this.save(conversations);
    }
  }

  static deleteConversation(conversationId: string): void {
    const conversations = this.load();
    const filtered = conversations.filter(c => c.id !== conversationId);
    this.save(filtered);
  }

  static clear(): void {
    localStorage.removeItem(STORAGE_KEYS.CONVERSATIONS);
  }

  /**
   * Remove duplicate conversations based on title and creation time
   */
  static removeDuplicates(): void {
    const conversations = this.load();
    const seen = new Map<string, Conversation>();
    const unique: Conversation[] = [];

    // Sort by creation time (newest first)
    conversations.sort((a, b) => b.createdAt - a.createdAt);

    for (const conv of conversations) {
      const key = `${conv.title}_${Math.floor(conv.createdAt / 60000)}`; // Group by title and minute
      if (!seen.has(key)) {
        seen.set(key, conv);
        unique.push(conv);
      }
    }

    this.save(unique);
  }
}

/**
 * Storage for app settings
 */
export class SettingsStorage {
  static save(settings: AppSettings): void {
    try {
      const data = JSON.stringify(settings);
      localStorage.setItem(STORAGE_KEYS.SETTINGS, data);
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw new Error('Failed to save settings');
    }
  }

  static load(): AppSettings {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.SETTINGS);
      return data ? { ...DEFAULT_SETTINGS, ...JSON.parse(data) } : DEFAULT_SETTINGS;
    } catch (error) {
      console.error('Failed to load settings:', error);
      return DEFAULT_SETTINGS;
    }
  }

  static clear(): void {
    localStorage.removeItem(STORAGE_KEYS.SETTINGS);
  }
}

/**
 * Storage for user preferences
 */
export class PreferencesStorage {
  static save(preferences: UserPreferences): void {
    try {
      const data = JSON.stringify(preferences);
      localStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, data);
    } catch (error) {
      console.error('Failed to save preferences:', error);
      throw new Error('Failed to save preferences');
    }
  }

  static load(): UserPreferences {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      return data ? { ...DEFAULT_PREFERENCES, ...JSON.parse(data) } : DEFAULT_PREFERENCES;
    } catch (error) {
      console.error('Failed to load preferences:', error);
      return DEFAULT_PREFERENCES;
    }
  }

  static clear(): void {
    localStorage.removeItem(STORAGE_KEYS.USER_PREFERENCES);
  }
}

/**
 * Clear all application data
 */
export function clearAllData(): void {
  ApiConfigStorage.clear();
  ConversationStorage.clear();
  SettingsStorage.clear();
  PreferencesStorage.clear();
}
