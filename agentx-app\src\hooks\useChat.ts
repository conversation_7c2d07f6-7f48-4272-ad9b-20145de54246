/**
 * React hook for managing chat functionality with streaming responses
 */

'use client';

import React, { useState, useCallback, useRef } from 'react';
import { openaiClient, ChatMessage } from '@/utils/openai-client';
import { ConversationStorage, Conversation, Message } from '@/utils/storage';

export interface UseChatOptions {
  conversationId?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
}

export interface UseChatReturn {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
  regenerateLastResponse: () => Promise<void>;
  stopGeneration: () => void;
}

/**
 * Custom hook for chat functionality
 */
export function useChat(options: UseChatOptions = {}): UseChatReturn {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const currentResponseRef = useRef<string>('');

  const {
    conversationId,
    temperature = 0.7,
    maxTokens = 2048,
    systemPrompt = 'You are a helpful AI assistant.',
  } = options;

  /**
   * Load conversation messages
   */
  const loadConversation = useCallback((id: string) => {
    const conversations = ConversationStorage.load();
    const conversation = conversations.find(c => c.id === id);
    if (conversation) {
      setMessages(conversation.messages);
    }
  }, []);

  /**
   * Save conversation to storage
   */
  const saveConversation = useCallback((msgs: Message[], convId?: string) => {
    if (msgs.length === 0) return;

    // Use existing conversationId or generate a new one only if none exists
    const targetId = conversationId || convId || `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const conversations = ConversationStorage.load();
    const existingIndex = conversations.findIndex(c => c.id === targetId);

    const conversation: Conversation = {
      id: targetId,
      title: msgs.find(m => m.role === 'user')?.content.slice(0, 50) || 'New Conversation',
      messages: msgs,
      createdAt: existingIndex === -1 ? Date.now() : conversations[existingIndex].createdAt,
      updatedAt: Date.now(),
    };

    if (existingIndex === -1) {
      ConversationStorage.addConversation(conversation);
    } else {
      ConversationStorage.updateConversation(targetId, conversation);
    }

    return targetId;
  }, [conversationId]);

  /**
   * Generate unique message ID
   */
  const generateMessageId = (): string => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  /**
   * Convert messages to OpenAI format
   */
  const convertToOpenAIMessages = (msgs: Message[]): ChatMessage[] => {
    const openaiMessages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
    ];

    msgs.forEach(msg => {
      openaiMessages.push({
        role: msg.role,
        content: msg.content,
      });
    });

    return openaiMessages;
  };

  /**
   * Send a message and get AI response
   */
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading) return;

    setError(null);
    setIsLoading(true);

    // Create user message
    const userMessage: Message = {
      id: generateMessageId(),
      content: content.trim(),
      role: 'user',
      timestamp: Date.now(),
    };

    // Create placeholder for assistant message
    const assistantMessage: Message = {
      id: generateMessageId(),
      content: '',
      role: 'assistant',
      timestamp: Date.now(),
    };

    const newMessages = [...messages, userMessage, assistantMessage];
    setMessages(newMessages);

    try {
      // Initialize OpenAI client if needed
      if (!openaiClient.isReady()) {
        await openaiClient.initialize();
      }

      // Create abort controller for this request
      abortControllerRef.current = new AbortController();
      currentResponseRef.current = '';

      const openaiMessages = convertToOpenAIMessages([...messages, userMessage]);

      // Start streaming response
      await openaiClient.createStreamingChatCompletion(
        {
          messages: openaiMessages,
          temperature,
          maxTokens,
        },
        {
          onUpdate: (content: string) => {
            currentResponseRef.current = content;
            setMessages(prev => {
              const updated = [...prev];
              const lastIndex = updated.length - 1;
              if (updated[lastIndex]?.role === 'assistant') {
                updated[lastIndex] = {
                  ...updated[lastIndex],
                  content,
                };
              }
              return updated;
            });
          },
          onSuccess: (content: string) => {
            setMessages(prev => {
              const updated = [...prev];
              const lastIndex = updated.length - 1;
              if (updated[lastIndex]?.role === 'assistant') {
                updated[lastIndex] = {
                  ...updated[lastIndex],
                  content,
                };
              }

              // Save conversation and get the conversation ID
              const savedConversationId = saveConversation(updated);

              // If this is a new conversation (no conversationId), trigger a page update
              if (!conversationId && savedConversationId) {
                // This will be handled by the parent component
                window.dispatchEvent(new CustomEvent('conversationCreated', {
                  detail: { conversationId: savedConversationId }
                }));
              }

              return updated;
            });
            setIsLoading(false);
          },
          onError: (err: Error) => {
            setError(err.message);
            setIsLoading(false);
            // Remove the placeholder assistant message on error
            setMessages(prev => prev.slice(0, -1));
          },
        }
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setIsLoading(false);
      // Remove the placeholder assistant message on error
      setMessages(prev => prev.slice(0, -1));
    }
  }, [messages, isLoading, temperature, maxTokens, systemPrompt, saveConversation]);

  /**
   * Clear all messages
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  /**
   * Regenerate the last assistant response
   */
  const regenerateLastResponse = useCallback(async () => {
    if (messages.length < 2 || isLoading) return;

    // Find the last user message
    const lastUserMessageIndex = messages.findLastIndex(msg => msg.role === 'user');
    if (lastUserMessageIndex === -1) return;

    const lastUserMessage = messages[lastUserMessageIndex];
    const messagesUpToUser = messages.slice(0, lastUserMessageIndex);

    setMessages(messagesUpToUser);
    await sendMessage(lastUserMessage.content);
  }, [messages, isLoading, sendMessage]);

  /**
   * Stop current generation
   */
  const stopGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsLoading(false);
  }, []);

  // Load conversation on mount if conversationId is provided
  React.useEffect(() => {
    if (conversationId) {
      loadConversation(conversationId);
    } else {
      // Clear messages when no conversation is selected
      setMessages([]);
    }
  }, [conversationId, loadConversation]);

  // Listen for new conversation events
  React.useEffect(() => {
    const handleNewConversation = () => {
      clearMessages();
    };

    window.addEventListener('newConversation', handleNewConversation);

    return () => {
      window.removeEventListener('newConversation', handleNewConversation);
    };
  }, [clearMessages]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    regenerateLastResponse,
    stopGeneration,
  };
}
