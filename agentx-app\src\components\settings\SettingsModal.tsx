'use client';

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Button,
  Tabs,
  Space,
  Alert,
  Typography,
  Divider,
  Switch,
  Select,
  App,
} from 'antd';
import { ApiConfigStorage, SettingsStorage, PreferencesStorage } from '@/utils/storage';
import { openaiClient } from '@/utils/openai-client';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface SettingsModalProps {
  open: boolean;
  onClose: () => void;
}

export default function SettingsModal({ open, onClose }: SettingsModalProps) {
  const { message, modal } = App.useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);
  const [activeTab, setActiveTab] = useState('api');

  // Load current settings when modal opens
  useEffect(() => {
    if (open) {
      loadSettings();
    }
  }, [open]);

  const loadSettings = async () => {
    try {
      const [apiConfig, appSettings, userPreferences] = await Promise.all([
        ApiConfigStorage.load(),
        SettingsStorage.load(),
        PreferencesStorage.load(),
      ]);

      form.setFieldsValue({
        // API Configuration
        apiKey: apiConfig.apiKey,
        baseUrl: apiConfig.baseUrl,
        modelName: apiConfig.modelName,

        // App Settings
        temperature: appSettings.temperature,
        maxTokens: appSettings.maxTokens,
        systemPrompt: appSettings.systemPrompt,
        theme: appSettings.theme,

        // User Preferences
        language: userPreferences.language,
        autoSave: userPreferences.autoSave,
        showTimestamps: userPreferences.showTimestamps,
      });
    } catch (error) {
      console.error('Failed to load settings:', error);
      message.error('Failed to load settings');
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // Save API configuration
      await ApiConfigStorage.save({
        apiKey: values.apiKey,
        baseUrl: values.baseUrl,
        modelName: values.modelName,
      });

      // Save app settings
      SettingsStorage.save({
        temperature: values.temperature,
        maxTokens: values.maxTokens,
        systemPrompt: values.systemPrompt,
        theme: values.theme,
      });

      // Save user preferences
      PreferencesStorage.save({
        language: values.language,
        autoSave: values.autoSave,
        showTimestamps: values.showTimestamps,
      });

      // Update OpenAI client
      await openaiClient.updateConfig(
        values.apiKey,
        values.baseUrl,
        values.modelName
      );

      message.success('Settings saved successfully');
      onClose();
    } catch (error) {
      console.error('Failed to save settings:', error);
      message.error('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      setTestingConnection(true);
      const values = await form.validateFields(['apiKey', 'baseUrl', 'modelName']);

      const isValid = await openaiClient.validateConfig(
        values.apiKey,
        values.baseUrl,
        values.modelName
      );

      if (isValid) {
        message.success('Connection test successful!');
      } else {
        message.error('Connection test failed. Please check your configuration.');
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      message.error('Connection test failed. Please check your configuration.');
    } finally {
      setTestingConnection(false);
    }
  };

  const handleReset = () => {
    modal.confirm({
      title: 'Reset Settings',
      content: 'Are you sure you want to reset all settings to default values?',
      onOk: () => {
        form.resetFields();
        message.info('Settings reset to default values');
      },
    });
  };

  const apiConfigTab = (
    <div>
      <Title level={4}>OpenAI API Configuration</Title>
      <Alert
        message="Security Notice"
        description="Your API key is encrypted and stored locally in your browser. It never leaves your device."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Form.Item
        name="apiKey"
        label="API Key"
        rules={[{ required: true, message: 'Please enter your OpenAI API key' }]}
      >
        <Input.Password
          placeholder="sk-..."
          style={{ fontFamily: 'monospace' }}
        />
      </Form.Item>

      <Form.Item
        name="baseUrl"
        label="Base URL"
        rules={[{ required: true, message: 'Please enter the API base URL' }]}
      >
        <Input placeholder="https://api.openai.com/v1" />
      </Form.Item>

      <Form.Item
        name="modelName"
        label="Model"
        rules={[{ required: true, message: 'Please select a model' }]}
      >
        <Select
          placeholder="Select a model"
          options={[
            { label: 'GPT-4o', value: 'gpt-4o' },
            { label: 'GPT-4o Mini', value: 'gpt-4o-mini' },
            { label: 'GPT-4 Turbo', value: 'gpt-4-turbo' },
            { label: 'GPT-4', value: 'gpt-4' },
            { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
          ]}
        />
      </Form.Item>

      <Space>
        <Button
          type="default"
          onClick={handleTestConnection}
          loading={testingConnection}
        >
          Test Connection
        </Button>
      </Space>
    </div>
  );

  const modelSettingsTab = (
    <div>
      <Title level={4}>Model Parameters</Title>

      <Form.Item
        name="temperature"
        label="Temperature"
        tooltip="Controls randomness. Lower values make responses more focused and deterministic."
      >
        <InputNumber
          min={0}
          max={2}
          step={0.1}
          style={{ width: '100%' }}
          placeholder="0.7"
        />
      </Form.Item>

      <Form.Item
        name="maxTokens"
        label="Max Tokens"
        tooltip="Maximum number of tokens in the response."
      >
        <InputNumber
          min={1}
          max={4096}
          style={{ width: '100%' }}
          placeholder="2048"
        />
      </Form.Item>

      <Form.Item
        name="systemPrompt"
        label="System Prompt"
        tooltip="Instructions that guide the AI's behavior and personality."
      >
        <TextArea
          rows={4}
          placeholder="You are a helpful AI assistant."
        />
      </Form.Item>
    </div>
  );

  const preferencesTab = (
    <div>
      <Title level={4}>User Preferences</Title>

      <Form.Item
        name="theme"
        label="Theme"
      >
        <Select
          options={[
            { label: 'Auto', value: 'auto' },
            { label: 'Light', value: 'light' },
            { label: 'Dark', value: 'dark' },
          ]}
        />
      </Form.Item>

      <Form.Item
        name="language"
        label="Language"
      >
        <Select
          options={[
            { label: 'English', value: 'en' },
            { label: '中文', value: 'zh' },
          ]}
        />
      </Form.Item>

      <Form.Item
        name="autoSave"
        label="Auto Save Conversations"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        name="showTimestamps"
        label="Show Message Timestamps"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>
    </div>
  );

  const tabItems = [
    {
      key: 'api',
      label: 'API Configuration',
      children: apiConfigTab,
    },
    {
      key: 'model',
      label: 'Model Settings',
      children: modelSettingsTab,
    },
    {
      key: 'preferences',
      label: 'Preferences',
      children: preferencesTab,
    },
  ];

  return (
    <Modal
      title="Settings"
      open={open}
      onCancel={onClose}
      width={700}
      footer={
        <Space>
          <Button onClick={handleReset}>
            Reset to Default
          </Button>
          <Button onClick={onClose}>
            Cancel
          </Button>
          <Button
            type="primary"
            onClick={handleSave}
            loading={loading}
          >
            Save Settings
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          temperature: 0.7,
          maxTokens: 2048,
          systemPrompt: 'You are a helpful AI assistant.',
          theme: 'auto',
          language: 'en',
          autoSave: true,
          showTimestamps: true,
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      </Form>
    </Modal>
  );
}
