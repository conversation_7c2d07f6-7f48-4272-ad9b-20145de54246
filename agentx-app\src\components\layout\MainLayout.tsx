'use client';

import React, { useState, useEffect } from 'react';
import { Layout, Drawer } from 'antd';
import Header from './Header';
import Sidebar from './Sidebar';
import { ConversationStorage, Conversation } from '@/utils/storage';

const { Content } = Layout;

interface MainLayoutProps {
  children: React.ReactNode;
  onSettingsClick: () => void;
  activeConversationId?: string;
  onConversationChange?: (conversationId: string | undefined) => void;
}

export default function MainLayout({
  children,
  onSettingsClick,
  activeConversationId,
  onConversationChange,
}: MainLayoutProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);

  // Check if mobile on mount and window resize
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (mobile) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Load conversations on mount and when activeConversationId changes
  useEffect(() => {
    loadConversations();
  }, []);

  // Reload conversations when a new conversation is created
  useEffect(() => {
    const handleConversationCreated = () => {
      loadConversations();
    };

    window.addEventListener('conversationCreated', handleConversationCreated);

    return () => {
      window.removeEventListener('conversationCreated', handleConversationCreated);
    };
  }, []);

  const loadConversations = () => {
    // Clean up duplicates before loading
    ConversationStorage.removeDuplicates();
    const loadedConversations = ConversationStorage.load();
    setConversations(loadedConversations);
  };

  const handleNewConversation = () => {
    // Clear the current conversation to start fresh
    onConversationChange?.(undefined);

    // Trigger a custom event to clear messages in the chat interface
    window.dispatchEvent(new CustomEvent('newConversation'));

    if (isMobile) {
      setMobileDrawerOpen(false);
    }
  };

  const handleSelectConversation = (id: string) => {
    onConversationChange?.(id);
    if (isMobile) {
      setMobileDrawerOpen(false);
    }
  };

  const handleDeleteConversation = (id: string) => {
    ConversationStorage.deleteConversation(id);
    loadConversations();

    // If the deleted conversation was active, clear the active conversation
    if (activeConversationId === id) {
      onConversationChange?.(undefined);
    }
  };

  const handleMenuToggle = () => {
    if (isMobile) {
      setMobileDrawerOpen(!mobileDrawerOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  const sidebarProps = {
    conversations,
    activeConversationId,
    onNewConversation: handleNewConversation,
    onSelectConversation: handleSelectConversation,
    onDeleteConversation: handleDeleteConversation,
    collapsed: sidebarCollapsed,
    onCollapse: setSidebarCollapsed,
    isMobile,
  };

  return (
    <Layout style={{ height: '100vh', overflow: 'hidden' }}>
      <Header
        onSettingsClick={onSettingsClick}
        onMenuToggle={handleMenuToggle}
        isMobile={isMobile}
      />

      <Layout style={{ height: 'calc(100vh - 64px)' }}>
        {isMobile ? (
          <Drawer
            title="Conversations"
            placement="left"
            onClose={() => setMobileDrawerOpen(false)}
            open={mobileDrawerOpen}
            bodyStyle={{ padding: 0 }}
            width={280}
          >
            <Sidebar {...sidebarProps} collapsed={false} />
          </Drawer>
        ) : (
          <Sidebar {...sidebarProps} />
        )}

        <Content
          style={{
            background: '#fff',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  );
}
