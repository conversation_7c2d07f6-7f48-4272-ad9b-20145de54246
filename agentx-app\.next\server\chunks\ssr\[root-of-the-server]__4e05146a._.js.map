{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Layout, Typography, Button, Space } from 'antd';\nimport { SettingOutlined, MenuOutlined } from '@ant-design/icons';\n\nconst { Header: AntHeader } = Layout;\nconst { Title } = Typography;\n\ninterface HeaderProps {\n  onSettingsClick: () => void;\n  onMenuToggle: () => void;\n  isMobile?: boolean;\n}\n\nexport default function Header({ onSettingsClick, onMenuToggle, isMobile = false }: HeaderProps) {\n  return (\n    <AntHeader\n      style={{\n        background: '#fff',\n        padding: '0 24px',\n        borderBottom: '1px solid #f0f0f0',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        height: 64,\n      }}\n    >\n      <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>\n        {isMobile && (\n          <Button\n            type=\"text\"\n            icon={<MenuOutlined />}\n            onClick={onMenuToggle}\n            style={{ padding: '4px 8px' }}\n          />\n        )}\n        <Title level={3} style={{ margin: 0, color: '#1677ff' }}>\n          AgentX\n        </Title>\n      </div>\n\n      <Space>\n        <Button\n          type=\"text\"\n          icon={<SettingOutlined />}\n          onClick={onSettingsClick}\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            width: 40,\n            height: 40,\n          }}\n          title=\"Settings\"\n        />\n      </Space>\n    </AntHeader>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AAAA;AAJA;;;;AAMA,MAAM,EAAE,QAAQ,SAAS,EAAE,GAAG,kLAAA,CAAA,SAAM;AACpC,MAAM,EAAE,KAAK,EAAE,GAAG,0LAAA,CAAA,aAAU;AAQb,SAAS,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,WAAW,KAAK,EAAe;IAC7F,qBACE,8OAAC;QACC,OAAO;YACL,YAAY;YACZ,SAAS;YACT,cAAc;YACd,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;QACV;;0BAEA,8OAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,YAAY;oBAAU,KAAK;gBAAG;;oBAC1D,0BACC,8OAAC,kMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wBACnB,SAAS;wBACT,OAAO;4BAAE,SAAS;wBAAU;;;;;;kCAGhC,8OAAC;wBAAM,OAAO;wBAAG,OAAO;4BAAE,QAAQ;4BAAG,OAAO;wBAAU;kCAAG;;;;;;;;;;;;0BAK3D,8OAAC,gMAAA,CAAA,QAAK;0BACJ,cAAA,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;oBACtB,SAAS;oBACT,OAAO;wBACL,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,OAAO;wBACP,QAAQ;oBACV;oBACA,OAAM;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Layout, Button, List, Typography, Space, Popconfirm } from 'antd';\nimport { PlusOutlined, DeleteOutlined, MessageOutlined } from '@ant-design/icons';\nimport { Conversation } from '@/utils/storage';\n\nconst { Sider } = Layout;\nconst { Text } = Typography;\n\ninterface SidebarProps {\n  conversations: Conversation[];\n  activeConversationId?: string;\n  onNewConversation: () => void;\n  onSelectConversation: (id: string) => void;\n  onDeleteConversation: (id: string) => void;\n  collapsed: boolean;\n  onCollapse: (collapsed: boolean) => void;\n  isMobile?: boolean;\n}\n\nexport default function Sidebar({\n  conversations,\n  activeConversationId,\n  onNewConversation,\n  onSelectConversation,\n  onDeleteConversation,\n  collapsed,\n  onCollapse,\n  isMobile = false,\n}: SidebarProps) {\n  const formatDate = (timestamp: number) => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n\n    if (diffInHours < 24) {\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    } else if (diffInHours < 24 * 7) {\n      return date.toLocaleDateString([], { weekday: 'short' });\n    } else {\n      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });\n    }\n  };\n\n  const truncateTitle = (title: string, maxLength: number = 30) => {\n    return title.length > maxLength ? `${title.slice(0, maxLength)}...` : title;\n  };\n\n  return (\n    <Sider\n      width={280}\n      collapsedWidth={isMobile ? 0 : 80}\n      collapsed={collapsed}\n      onCollapse={onCollapse}\n      style={{\n        background: '#fafafa',\n        borderRight: '1px solid #f0f0f0',\n        height: '100%',\n        overflow: 'hidden',\n      }}\n      breakpoint=\"lg\"\n      collapsible={!isMobile}\n      trigger={null}\n    >\n      <div\n        style={{\n          padding: collapsed ? '16px 8px' : '16px',\n          borderBottom: '1px solid #f0f0f0',\n        }}\n      >\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={onNewConversation}\n          block={!collapsed}\n          style={{\n            height: 40,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: collapsed ? 'center' : 'flex-start',\n          }}\n        >\n          {!collapsed && 'New Chat'}\n        </Button>\n      </div>\n\n      <div\n        style={{\n          flex: 1,\n          overflow: 'auto',\n          padding: collapsed ? '8px 4px' : '8px',\n        }}\n      >\n        <List\n          dataSource={conversations}\n          renderItem={(conversation) => (\n            <List.Item\n              key={conversation.id}\n              style={{\n                padding: collapsed ? '8px 4px' : '8px 12px',\n                margin: '4px 0',\n                borderRadius: 8,\n                cursor: 'pointer',\n                background: activeConversationId === conversation.id ? '#e6f4ff' : 'transparent',\n                border: activeConversationId === conversation.id ? '1px solid #91caff' : '1px solid transparent',\n                transition: 'all 0.2s',\n              }}\n              onClick={() => onSelectConversation(conversation.id)}\n              onMouseEnter={(e) => {\n                if (activeConversationId !== conversation.id) {\n                  e.currentTarget.style.background = '#f5f5f5';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (activeConversationId !== conversation.id) {\n                  e.currentTarget.style.background = 'transparent';\n                }\n              }}\n            >\n              <div style={{ width: '100%', minWidth: 0 }}>\n                {collapsed ? (\n                  <div\n                    style={{\n                      display: 'flex',\n                      justifyContent: 'center',\n                      alignItems: 'center',\n                      height: 32,\n                    }}\n                  >\n                    <MessageOutlined style={{ fontSize: 16, color: '#666' }} />\n                  </div>\n                ) : (\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\n                    <div style={{ flex: 1, minWidth: 0, marginRight: 8 }}>\n                      <Text\n                        strong\n                        style={{\n                          fontSize: 14,\n                          color: '#262626',\n                          display: 'block',\n                          marginBottom: 4,\n                          overflow: 'hidden',\n                          textOverflow: 'ellipsis',\n                          whiteSpace: 'nowrap',\n                        }}\n                      >\n                        {truncateTitle(conversation.title)}\n                      </Text>\n                      <Text\n                        type=\"secondary\"\n                        style={{\n                          fontSize: 12,\n                          display: 'block',\n                        }}\n                      >\n                        {formatDate(conversation.updatedAt)}\n                      </Text>\n                    </div>\n                    <Popconfirm\n                      title=\"Delete conversation\"\n                      description=\"Are you sure you want to delete this conversation?\"\n                      onConfirm={(e) => {\n                        e?.stopPropagation();\n                        onDeleteConversation(conversation.id);\n                      }}\n                      okText=\"Yes\"\n                      cancelText=\"No\"\n                      placement=\"topRight\"\n                    >\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<DeleteOutlined />}\n                        style={{\n                          opacity: 0.6,\n                          padding: '2px 4px',\n                          height: 24,\n                          width: 24,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                        }}\n                        onClick={(e) => e.stopPropagation()}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.opacity = '1';\n                          e.currentTarget.style.background = '#ff4d4f';\n                          e.currentTarget.style.color = '#fff';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.opacity = '0.6';\n                          e.currentTarget.style.background = 'transparent';\n                          e.currentTarget.style.color = 'inherit';\n                        }}\n                      />\n                    </Popconfirm>\n                  </div>\n                )}\n              </div>\n            </List.Item>\n          )}\n          locale={{ emptyText: collapsed ? '' : 'No conversations yet' }}\n        />\n      </div>\n    </Sider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAJA;;;;AAOA,MAAM,EAAE,KAAK,EAAE,GAAG,kLAAA,CAAA,SAAM;AACxB,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAaZ,SAAS,QAAQ,EAC9B,aAAa,EACb,oBAAoB,EACpB,iBAAiB,EACjB,oBAAoB,EACpB,oBAAoB,EACpB,SAAS,EACT,UAAU,EACV,WAAW,KAAK,EACH;IACb,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEtE,IAAI,cAAc,IAAI;YACpB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QAC1E,OAAO,IAAI,cAAc,KAAK,GAAG;YAC/B,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,SAAS;YAAQ;QACxD,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,KAAK;YAAU;QACtE;IACF;IAEA,MAAM,gBAAgB,CAAC,OAAe,YAAoB,EAAE;QAC1D,OAAO,MAAM,MAAM,GAAG,YAAY,GAAG,MAAM,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;IACxE;IAEA,qBACE,8OAAC;QACC,OAAO;QACP,gBAAgB,WAAW,IAAI;QAC/B,WAAW;QACX,YAAY;QACZ,OAAO;YACL,YAAY;YACZ,aAAa;YACb,QAAQ;YACR,UAAU;QACZ;QACA,YAAW;QACX,aAAa,CAAC;QACd,SAAS;;0BAET,8OAAC;gBACC,OAAO;oBACL,SAAS,YAAY,aAAa;oBAClC,cAAc;gBAChB;0BAEA,cAAA,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,SAAS;oBACT,OAAO,CAAC;oBACR,OAAO;wBACL,QAAQ;wBACR,SAAS;wBACT,YAAY;wBACZ,gBAAgB,YAAY,WAAW;oBACzC;8BAEC,CAAC,aAAa;;;;;;;;;;;0BAInB,8OAAC;gBACC,OAAO;oBACL,MAAM;oBACN,UAAU;oBACV,SAAS,YAAY,YAAY;gBACnC;0BAEA,cAAA,8OAAC,8KAAA,CAAA,OAAI;oBACH,YAAY;oBACZ,YAAY,CAAC,6BACX,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BAER,OAAO;gCACL,SAAS,YAAY,YAAY;gCACjC,QAAQ;gCACR,cAAc;gCACd,QAAQ;gCACR,YAAY,yBAAyB,aAAa,EAAE,GAAG,YAAY;gCACnE,QAAQ,yBAAyB,aAAa,EAAE,GAAG,sBAAsB;gCACzE,YAAY;4BACd;4BACA,SAAS,IAAM,qBAAqB,aAAa,EAAE;4BACnD,cAAc,CAAC;gCACb,IAAI,yBAAyB,aAAa,EAAE,EAAE;oCAC5C,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;gCACrC;4BACF;4BACA,cAAc,CAAC;gCACb,IAAI,yBAAyB,aAAa,EAAE,EAAE;oCAC5C,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;gCACrC;4BACF;sCAEA,cAAA,8OAAC;gCAAI,OAAO;oCAAE,OAAO;oCAAQ,UAAU;gCAAE;0CACtC,0BACC,8OAAC;oCACC,OAAO;wCACL,SAAS;wCACT,gBAAgB;wCAChB,YAAY;wCACZ,QAAQ;oCACV;8CAEA,cAAA,8OAAC,wNAAA,CAAA,kBAAe;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;wCAAO;;;;;;;;;;2DAGxD,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,gBAAgB;wCAAiB,YAAY;oCAAa;;sDACvF,8OAAC;4CAAI,OAAO;gDAAE,MAAM;gDAAG,UAAU;gDAAG,aAAa;4CAAE;;8DACjD,8OAAC;oDACC,MAAM;oDACN,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,SAAS;wDACT,cAAc;wDACd,UAAU;wDACV,cAAc;wDACd,YAAY;oDACd;8DAEC,cAAc,aAAa,KAAK;;;;;;8DAEnC,8OAAC;oDACC,MAAK;oDACL,OAAO;wDACL,UAAU;wDACV,SAAS;oDACX;8DAEC,WAAW,aAAa,SAAS;;;;;;;;;;;;sDAGtC,8OAAC,0LAAA,CAAA,aAAU;4CACT,OAAM;4CACN,aAAY;4CACZ,WAAW,CAAC;gDACV,GAAG;gDACH,qBAAqB,aAAa,EAAE;4CACtC;4CACA,QAAO;4CACP,YAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,MAAK;gDACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;gDACrB,OAAO;oDACL,SAAS;oDACT,SAAS;oDACT,QAAQ;oDACR,OAAO;oDACP,SAAS;oDACT,YAAY;oDACZ,gBAAgB;gDAClB;gDACA,SAAS,CAAC,IAAM,EAAE,eAAe;gDACjC,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;oDAChC,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;oDAChC,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;;;;;;;;;;;;;;;;;;;;;;2BA/FL,aAAa,EAAE;;;;;oBAuGxB,QAAQ;wBAAE,WAAW,YAAY,KAAK;oBAAuB;;;;;;;;;;;;;;;;;AAKvE", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/utils/encryption.ts"], "sourcesContent": ["/**\n * Encryption utilities for secure local storage\n * Uses Web Crypto API for client-side encryption\n */\n\n// Generate a key from a password using PBKDF2\nasync function deriveKey(password: string, salt: Uint8Array): Promise<CryptoKey> {\n  const encoder = new TextEncoder();\n  const keyMaterial = await crypto.subtle.importKey(\n    'raw',\n    encoder.encode(password),\n    { name: 'PBKDF2' },\n    false,\n    ['deriveKey']\n  );\n\n  return crypto.subtle.deriveKey(\n    {\n      name: 'PBKDF2',\n      salt: salt,\n      iterations: 100000,\n      hash: 'SHA-256',\n    },\n    keyMaterial,\n    { name: 'AES-GCM', length: 256 },\n    false,\n    ['encrypt', 'decrypt']\n  );\n}\n\n// Generate a random salt\nfunction generateSalt(): Uint8Array {\n  return crypto.getRandomValues(new Uint8Array(16));\n}\n\n// Generate a random IV\nfunction generateIV(): Uint8Array {\n  return crypto.getRandomValues(new Uint8Array(12));\n}\n\n// Convert ArrayBuffer to base64 string\nfunction arrayBufferToBase64(buffer: ArrayBuffer): string {\n  const bytes = new Uint8Array(buffer);\n  let binary = '';\n  for (let i = 0; i < bytes.byteLength; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return btoa(binary);\n}\n\n// Convert base64 string to ArrayBuffer\nfunction base64ToArrayBuffer(base64: string): ArrayBuffer {\n  const binary = atob(base64);\n  const bytes = new Uint8Array(binary.length);\n  for (let i = 0; i < binary.length; i++) {\n    bytes[i] = binary.charCodeAt(i);\n  }\n  return bytes.buffer;\n}\n\n// Get or create a master key for the application\nasync function getMasterKey(): Promise<string> {\n  const stored = localStorage.getItem('agentx_master_key');\n  if (stored) {\n    return stored;\n  }\n  \n  // Generate a new master key\n  const key = crypto.getRandomValues(new Uint8Array(32));\n  const keyString = arrayBufferToBase64(key);\n  localStorage.setItem('agentx_master_key', keyString);\n  return keyString;\n}\n\n/**\n * Encrypt a string value\n */\nexport async function encryptValue(value: string): Promise<string> {\n  try {\n    const masterKey = await getMasterKey();\n    const salt = generateSalt();\n    const iv = generateIV();\n    \n    const key = await deriveKey(masterKey, salt);\n    const encoder = new TextEncoder();\n    const data = encoder.encode(value);\n    \n    const encrypted = await crypto.subtle.encrypt(\n      { name: 'AES-GCM', iv: iv },\n      key,\n      data\n    );\n    \n    // Combine salt, iv, and encrypted data\n    const combined = new Uint8Array(salt.length + iv.length + encrypted.byteLength);\n    combined.set(salt, 0);\n    combined.set(iv, salt.length);\n    combined.set(new Uint8Array(encrypted), salt.length + iv.length);\n    \n    return arrayBufferToBase64(combined);\n  } catch (error) {\n    console.error('Encryption failed:', error);\n    throw new Error('Failed to encrypt value');\n  }\n}\n\n/**\n * Decrypt a string value\n */\nexport async function decryptValue(encryptedValue: string): Promise<string> {\n  try {\n    const masterKey = await getMasterKey();\n    const combined = new Uint8Array(base64ToArrayBuffer(encryptedValue));\n    \n    // Extract salt, iv, and encrypted data\n    const salt = combined.slice(0, 16);\n    const iv = combined.slice(16, 28);\n    const encrypted = combined.slice(28);\n    \n    const key = await deriveKey(masterKey, salt);\n    \n    const decrypted = await crypto.subtle.decrypt(\n      { name: 'AES-GCM', iv: iv },\n      key,\n      encrypted\n    );\n    \n    const decoder = new TextDecoder();\n    return decoder.decode(decrypted);\n  } catch (error) {\n    console.error('Decryption failed:', error);\n    throw new Error('Failed to decrypt value');\n  }\n}\n\n/**\n * Clear all encryption keys (for logout/reset)\n */\nexport function clearEncryptionKeys(): void {\n  localStorage.removeItem('agentx_master_key');\n}\n\n/**\n * Check if encryption is supported\n */\nexport function isEncryptionSupported(): boolean {\n  return typeof crypto !== 'undefined' && \n         typeof crypto.subtle !== 'undefined' &&\n         typeof crypto.getRandomValues !== 'undefined';\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,8CAA8C;;;;;;;AAC9C,eAAe,UAAU,QAAgB,EAAE,IAAgB;IACzD,MAAM,UAAU,IAAI;IACpB,MAAM,cAAc,MAAM,OAAO,MAAM,CAAC,SAAS,CAC/C,OACA,QAAQ,MAAM,CAAC,WACf;QAAE,MAAM;IAAS,GACjB,OACA;QAAC;KAAY;IAGf,OAAO,OAAO,MAAM,CAAC,SAAS,CAC5B;QACE,MAAM;QACN,MAAM;QACN,YAAY;QACZ,MAAM;IACR,GACA,aACA;QAAE,MAAM;QAAW,QAAQ;IAAI,GAC/B,OACA;QAAC;QAAW;KAAU;AAE1B;AAEA,yBAAyB;AACzB,SAAS;IACP,OAAO,OAAO,eAAe,CAAC,IAAI,WAAW;AAC/C;AAEA,uBAAuB;AACvB,SAAS;IACP,OAAO,OAAO,eAAe,CAAC,IAAI,WAAW;AAC/C;AAEA,uCAAuC;AACvC,SAAS,oBAAoB,MAAmB;IAC9C,MAAM,QAAQ,IAAI,WAAW;IAC7B,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,UAAU,EAAE,IAAK;QACzC,UAAU,OAAO,YAAY,CAAC,KAAK,CAAC,EAAE;IACxC;IACA,OAAO,KAAK;AACd;AAEA,uCAAuC;AACvC,SAAS,oBAAoB,MAAc;IACzC,MAAM,SAAS,KAAK;IACpB,MAAM,QAAQ,IAAI,WAAW,OAAO,MAAM;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,KAAK,CAAC,EAAE,GAAG,OAAO,UAAU,CAAC;IAC/B;IACA,OAAO,MAAM,MAAM;AACrB;AAEA,iDAAiD;AACjD,eAAe;IACb,MAAM,SAAS,aAAa,OAAO,CAAC;IACpC,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,MAAM,OAAO,eAAe,CAAC,IAAI,WAAW;IAClD,MAAM,YAAY,oBAAoB;IACtC,aAAa,OAAO,CAAC,qBAAqB;IAC1C,OAAO;AACT;AAKO,eAAe,aAAa,KAAa;IAC9C,IAAI;QACF,MAAM,YAAY,MAAM;QACxB,MAAM,OAAO;QACb,MAAM,KAAK;QAEX,MAAM,MAAM,MAAM,UAAU,WAAW;QACvC,MAAM,UAAU,IAAI;QACpB,MAAM,OAAO,QAAQ,MAAM,CAAC;QAE5B,MAAM,YAAY,MAAM,OAAO,MAAM,CAAC,OAAO,CAC3C;YAAE,MAAM;YAAW,IAAI;QAAG,GAC1B,KACA;QAGF,uCAAuC;QACvC,MAAM,WAAW,IAAI,WAAW,KAAK,MAAM,GAAG,GAAG,MAAM,GAAG,UAAU,UAAU;QAC9E,SAAS,GAAG,CAAC,MAAM;QACnB,SAAS,GAAG,CAAC,IAAI,KAAK,MAAM;QAC5B,SAAS,GAAG,CAAC,IAAI,WAAW,YAAY,KAAK,MAAM,GAAG,GAAG,MAAM;QAE/D,OAAO,oBAAoB;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,aAAa,cAAsB;IACvD,IAAI;QACF,MAAM,YAAY,MAAM;QACxB,MAAM,WAAW,IAAI,WAAW,oBAAoB;QAEpD,uCAAuC;QACvC,MAAM,OAAO,SAAS,KAAK,CAAC,GAAG;QAC/B,MAAM,KAAK,SAAS,KAAK,CAAC,IAAI;QAC9B,MAAM,YAAY,SAAS,KAAK,CAAC;QAEjC,MAAM,MAAM,MAAM,UAAU,WAAW;QAEvC,MAAM,YAAY,MAAM,OAAO,MAAM,CAAC,OAAO,CAC3C;YAAE,MAAM;YAAW,IAAI;QAAG,GAC1B,KACA;QAGF,MAAM,UAAU,IAAI;QACpB,OAAO,QAAQ,MAAM,CAAC;IACxB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAKO,SAAS;IACd,OAAO,OAAO,WAAW,eAClB,OAAO,OAAO,MAAM,KAAK,eACzB,OAAO,OAAO,eAAe,KAAK;AAC3C", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/utils/storage.ts"], "sourcesContent": ["/**\n * Secure local storage utilities for AgentX\n * Handles encryption/decryption of sensitive data\n */\n\nimport { encryptValue, decryptValue, isEncryptionSupported } from './encryption';\n\n// Storage keys\nconst STORAGE_KEYS = {\n  API_KEY: 'agentx_api_key',\n  API_BASE_URL: 'agentx_api_base_url',\n  MODEL_NAME: 'agentx_model_name',\n  CONVERSATIONS: 'agentx_conversations',\n  SETTINGS: 'agentx_settings',\n  USER_PREFERENCES: 'agentx_user_preferences',\n} as const;\n\n// Types\nexport interface ApiConfig {\n  apiKey: string;\n  baseUrl: string;\n  modelName: string;\n}\n\nexport interface Message {\n  id: string;\n  content: string;\n  role: 'user' | 'assistant';\n  timestamp: number;\n}\n\nexport interface Conversation {\n  id: string;\n  title: string;\n  messages: Message[];\n  createdAt: number;\n  updatedAt: number;\n}\n\nexport interface AppSettings {\n  temperature: number;\n  maxTokens: number;\n  systemPrompt: string;\n  theme: 'light' | 'dark' | 'auto';\n}\n\nexport interface UserPreferences {\n  language: string;\n  autoSave: boolean;\n  showTimestamps: boolean;\n}\n\n// Default values\nconst DEFAULT_API_CONFIG: ApiConfig = {\n  apiKey: '',\n  baseUrl: 'https://c-z0-api-01.hash070.com/v1',\n  modelName: 'gpt-4o-mini',\n};\n\nconst DEFAULT_SETTINGS: AppSettings = {\n  temperature: 0.7,\n  maxTokens: 2048,\n  systemPrompt: 'You are a helpful AI assistant.',\n  theme: 'auto',\n};\n\nconst DEFAULT_PREFERENCES: UserPreferences = {\n  language: 'en',\n  autoSave: true,\n  showTimestamps: true,\n};\n\n/**\n * Secure storage for API configuration\n */\nexport class ApiConfigStorage {\n  static async save(config: ApiConfig): Promise<void> {\n    try {\n      if (isEncryptionSupported() && config.apiKey) {\n        const encryptedKey = await encryptValue(config.apiKey);\n        localStorage.setItem(STORAGE_KEYS.API_KEY, encryptedKey);\n      }\n      \n      localStorage.setItem(STORAGE_KEYS.API_BASE_URL, config.baseUrl);\n      localStorage.setItem(STORAGE_KEYS.MODEL_NAME, config.modelName);\n    } catch (error) {\n      console.error('Failed to save API config:', error);\n      throw new Error('Failed to save API configuration');\n    }\n  }\n\n  static async load(): Promise<ApiConfig> {\n    try {\n      const baseUrl = localStorage.getItem(STORAGE_KEYS.API_BASE_URL) || DEFAULT_API_CONFIG.baseUrl;\n      const modelName = localStorage.getItem(STORAGE_KEYS.MODEL_NAME) || DEFAULT_API_CONFIG.modelName;\n      \n      let apiKey = '';\n      const encryptedKey = localStorage.getItem(STORAGE_KEYS.API_KEY);\n      \n      if (encryptedKey && isEncryptionSupported()) {\n        try {\n          apiKey = await decryptValue(encryptedKey);\n        } catch (error) {\n          console.warn('Failed to decrypt API key, using empty value');\n          apiKey = '';\n        }\n      }\n\n      return { apiKey, baseUrl, modelName };\n    } catch (error) {\n      console.error('Failed to load API config:', error);\n      return DEFAULT_API_CONFIG;\n    }\n  }\n\n  static clear(): void {\n    localStorage.removeItem(STORAGE_KEYS.API_KEY);\n    localStorage.removeItem(STORAGE_KEYS.API_BASE_URL);\n    localStorage.removeItem(STORAGE_KEYS.MODEL_NAME);\n  }\n}\n\n/**\n * Storage for conversations\n */\nexport class ConversationStorage {\n  static save(conversations: Conversation[]): void {\n    try {\n      const data = JSON.stringify(conversations);\n      localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, data);\n    } catch (error) {\n      console.error('Failed to save conversations:', error);\n      throw new Error('Failed to save conversations');\n    }\n  }\n\n  static load(): Conversation[] {\n    try {\n      const data = localStorage.getItem(STORAGE_KEYS.CONVERSATIONS);\n      return data ? JSON.parse(data) : [];\n    } catch (error) {\n      console.error('Failed to load conversations:', error);\n      return [];\n    }\n  }\n\n  static addConversation(conversation: Conversation): void {\n    const conversations = this.load();\n    conversations.unshift(conversation); // Add to beginning\n    this.save(conversations);\n  }\n\n  static updateConversation(conversationId: string, updates: Partial<Conversation>): void {\n    const conversations = this.load();\n    const index = conversations.findIndex(c => c.id === conversationId);\n    \n    if (index !== -1) {\n      conversations[index] = { ...conversations[index], ...updates, updatedAt: Date.now() };\n      this.save(conversations);\n    }\n  }\n\n  static deleteConversation(conversationId: string): void {\n    const conversations = this.load();\n    const filtered = conversations.filter(c => c.id !== conversationId);\n    this.save(filtered);\n  }\n\n  static clear(): void {\n    localStorage.removeItem(STORAGE_KEYS.CONVERSATIONS);\n  }\n}\n\n/**\n * Storage for app settings\n */\nexport class SettingsStorage {\n  static save(settings: AppSettings): void {\n    try {\n      const data = JSON.stringify(settings);\n      localStorage.setItem(STORAGE_KEYS.SETTINGS, data);\n    } catch (error) {\n      console.error('Failed to save settings:', error);\n      throw new Error('Failed to save settings');\n    }\n  }\n\n  static load(): AppSettings {\n    try {\n      const data = localStorage.getItem(STORAGE_KEYS.SETTINGS);\n      return data ? { ...DEFAULT_SETTINGS, ...JSON.parse(data) } : DEFAULT_SETTINGS;\n    } catch (error) {\n      console.error('Failed to load settings:', error);\n      return DEFAULT_SETTINGS;\n    }\n  }\n\n  static clear(): void {\n    localStorage.removeItem(STORAGE_KEYS.SETTINGS);\n  }\n}\n\n/**\n * Storage for user preferences\n */\nexport class PreferencesStorage {\n  static save(preferences: UserPreferences): void {\n    try {\n      const data = JSON.stringify(preferences);\n      localStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, data);\n    } catch (error) {\n      console.error('Failed to save preferences:', error);\n      throw new Error('Failed to save preferences');\n    }\n  }\n\n  static load(): UserPreferences {\n    try {\n      const data = localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);\n      return data ? { ...DEFAULT_PREFERENCES, ...JSON.parse(data) } : DEFAULT_PREFERENCES;\n    } catch (error) {\n      console.error('Failed to load preferences:', error);\n      return DEFAULT_PREFERENCES;\n    }\n  }\n\n  static clear(): void {\n    localStorage.removeItem(STORAGE_KEYS.USER_PREFERENCES);\n  }\n}\n\n/**\n * Clear all application data\n */\nexport function clearAllData(): void {\n  ApiConfigStorage.clear();\n  ConversationStorage.clear();\n  SettingsStorage.clear();\n  PreferencesStorage.clear();\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAED;;AAEA,eAAe;AACf,MAAM,eAAe;IACnB,SAAS;IACT,cAAc;IACd,YAAY;IACZ,eAAe;IACf,UAAU;IACV,kBAAkB;AACpB;AAqCA,iBAAiB;AACjB,MAAM,qBAAgC;IACpC,QAAQ;IACR,SAAS;IACT,WAAW;AACb;AAEA,MAAM,mBAAgC;IACpC,aAAa;IACb,WAAW;IACX,cAAc;IACd,OAAO;AACT;AAEA,MAAM,sBAAuC;IAC3C,UAAU;IACV,UAAU;IACV,gBAAgB;AAClB;AAKO,MAAM;IACX,aAAa,KAAK,MAAiB,EAAiB;QAClD,IAAI;YACF,IAAI,CAAA,GAAA,0HAAA,CAAA,wBAAqB,AAAD,OAAO,OAAO,MAAM,EAAE;gBAC5C,MAAM,eAAe,MAAM,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,MAAM;gBACrD,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE;YAC7C;YAEA,aAAa,OAAO,CAAC,aAAa,YAAY,EAAE,OAAO,OAAO;YAC9D,aAAa,OAAO,CAAC,aAAa,UAAU,EAAE,OAAO,SAAS;QAChE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,OAA2B;QACtC,IAAI;YACF,MAAM,UAAU,aAAa,OAAO,CAAC,aAAa,YAAY,KAAK,mBAAmB,OAAO;YAC7F,MAAM,YAAY,aAAa,OAAO,CAAC,aAAa,UAAU,KAAK,mBAAmB,SAAS;YAE/F,IAAI,SAAS;YACb,MAAM,eAAe,aAAa,OAAO,CAAC,aAAa,OAAO;YAE9D,IAAI,gBAAgB,CAAA,GAAA,0HAAA,CAAA,wBAAqB,AAAD,KAAK;gBAC3C,IAAI;oBACF,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;gBAC9B,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC;oBACb,SAAS;gBACX;YACF;YAEA,OAAO;gBAAE;gBAAQ;gBAAS;YAAU;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,OAAO,QAAc;QACnB,aAAa,UAAU,CAAC,aAAa,OAAO;QAC5C,aAAa,UAAU,CAAC,aAAa,YAAY;QACjD,aAAa,UAAU,CAAC,aAAa,UAAU;IACjD;AACF;AAKO,MAAM;IACX,OAAO,KAAK,aAA6B,EAAQ;QAC/C,IAAI;YACF,MAAM,OAAO,KAAK,SAAS,CAAC;YAC5B,aAAa,OAAO,CAAC,aAAa,aAAa,EAAE;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO,OAAuB;QAC5B,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC,aAAa,aAAa;YAC5D,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ,EAAE;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA,OAAO,gBAAgB,YAA0B,EAAQ;QACvD,MAAM,gBAAgB,IAAI,CAAC,IAAI;QAC/B,cAAc,OAAO,CAAC,eAAe,mBAAmB;QACxD,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA,OAAO,mBAAmB,cAAsB,EAAE,OAA8B,EAAQ;QACtF,MAAM,gBAAgB,IAAI,CAAC,IAAI;QAC/B,MAAM,QAAQ,cAAc,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAEpD,IAAI,UAAU,CAAC,GAAG;YAChB,aAAa,CAAC,MAAM,GAAG;gBAAE,GAAG,aAAa,CAAC,MAAM;gBAAE,GAAG,OAAO;gBAAE,WAAW,KAAK,GAAG;YAAG;YACpF,IAAI,CAAC,IAAI,CAAC;QACZ;IACF;IAEA,OAAO,mBAAmB,cAAsB,EAAQ;QACtD,MAAM,gBAAgB,IAAI,CAAC,IAAI;QAC/B,MAAM,WAAW,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA,OAAO,QAAc;QACnB,aAAa,UAAU,CAAC,aAAa,aAAa;IACpD;AACF;AAKO,MAAM;IACX,OAAO,KAAK,QAAqB,EAAQ;QACvC,IAAI;YACF,MAAM,OAAO,KAAK,SAAS,CAAC;YAC5B,aAAa,OAAO,CAAC,aAAa,QAAQ,EAAE;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO,OAAoB;QACzB,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC,aAAa,QAAQ;YACvD,OAAO,OAAO;gBAAE,GAAG,gBAAgB;gBAAE,GAAG,KAAK,KAAK,CAAC,KAAK;YAAC,IAAI;QAC/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA,OAAO,QAAc;QACnB,aAAa,UAAU,CAAC,aAAa,QAAQ;IAC/C;AACF;AAKO,MAAM;IACX,OAAO,KAAK,WAA4B,EAAQ;QAC9C,IAAI;YACF,MAAM,OAAO,KAAK,SAAS,CAAC;YAC5B,aAAa,OAAO,CAAC,aAAa,gBAAgB,EAAE;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO,OAAwB;QAC7B,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC,aAAa,gBAAgB;YAC/D,OAAO,OAAO;gBAAE,GAAG,mBAAmB;gBAAE,GAAG,KAAK,KAAK,CAAC,KAAK;YAAC,IAAI;QAClE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA,OAAO,QAAc;QACnB,aAAa,UAAU,CAAC,aAAa,gBAAgB;IACvD;AACF;AAKO,SAAS;IACd,iBAAiB,KAAK;IACtB,oBAAoB,KAAK;IACzB,gBAAgB,KAAK;IACrB,mBAAmB,KAAK;AAC1B", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Drawer } from 'antd';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport { ConversationStorage, Conversation } from '@/utils/storage';\n\nconst { Content } = Layout;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n  onSettingsClick: () => void;\n  activeConversationId?: string;\n  onConversationChange?: (conversationId: string | undefined) => void;\n}\n\nexport default function MainLayout({\n  children,\n  onSettingsClick,\n  activeConversationId,\n  onConversationChange,\n}: MainLayoutProps) {\n  const [conversations, setConversations] = useState<Conversation[]>([]);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);\n\n  // Check if mobile on mount and window resize\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth < 768;\n      setIsMobile(mobile);\n      if (mobile) {\n        setSidebarCollapsed(true);\n      }\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Load conversations on mount and when activeConversationId changes\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Reload conversations when a new conversation is created\n  useEffect(() => {\n    const handleConversationCreated = () => {\n      loadConversations();\n    };\n\n    window.addEventListener('conversationCreated', handleConversationCreated);\n\n    return () => {\n      window.removeEventListener('conversationCreated', handleConversationCreated);\n    };\n  }, []);\n\n  const loadConversations = () => {\n    const loadedConversations = ConversationStorage.load();\n    setConversations(loadedConversations);\n  };\n\n  const handleNewConversation = () => {\n    // Clear the current conversation to start fresh\n    onConversationChange?.(undefined);\n\n    // Trigger a custom event to clear messages in the chat interface\n    window.dispatchEvent(new CustomEvent('newConversation'));\n\n    if (isMobile) {\n      setMobileDrawerOpen(false);\n    }\n  };\n\n  const handleSelectConversation = (id: string) => {\n    onConversationChange?.(id);\n    if (isMobile) {\n      setMobileDrawerOpen(false);\n    }\n  };\n\n  const handleDeleteConversation = (id: string) => {\n    ConversationStorage.deleteConversation(id);\n    loadConversations();\n\n    // If the deleted conversation was active, clear the active conversation\n    if (activeConversationId === id) {\n      onConversationChange?.(undefined);\n    }\n  };\n\n  const handleMenuToggle = () => {\n    if (isMobile) {\n      setMobileDrawerOpen(!mobileDrawerOpen);\n    } else {\n      setSidebarCollapsed(!sidebarCollapsed);\n    }\n  };\n\n  const sidebarProps = {\n    conversations,\n    activeConversationId,\n    onNewConversation: handleNewConversation,\n    onSelectConversation: handleSelectConversation,\n    onDeleteConversation: handleDeleteConversation,\n    collapsed: sidebarCollapsed,\n    onCollapse: setSidebarCollapsed,\n    isMobile,\n  };\n\n  return (\n    <Layout style={{ height: '100vh', overflow: 'hidden' }}>\n      <Header\n        onSettingsClick={onSettingsClick}\n        onMenuToggle={handleMenuToggle}\n        isMobile={isMobile}\n      />\n\n      <Layout style={{ height: 'calc(100vh - 64px)' }}>\n        {isMobile ? (\n          <Drawer\n            title=\"Conversations\"\n            placement=\"left\"\n            onClose={() => setMobileDrawerOpen(false)}\n            open={mobileDrawerOpen}\n            bodyStyle={{ padding: 0 }}\n            width={280}\n          >\n            <Sidebar {...sidebarProps} collapsed={false} />\n          </Drawer>\n        ) : (\n          <Sidebar {...sidebarProps} />\n        )}\n\n        <Content\n          style={{\n            background: '#fff',\n            overflow: 'hidden',\n            display: 'flex',\n            flexDirection: 'column',\n          }}\n        >\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AASX,SAAS,WAAW,EACjC,QAAQ,EACR,eAAe,EACf,oBAAoB,EACpB,oBAAoB,EACJ;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,MAAM,SAAS,OAAO,UAAU,GAAG;YACnC,YAAY;YACZ,IAAI,QAAQ;gBACV,oBAAoB;YACtB;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,oEAAoE;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,4BAA4B;YAChC;QACF;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAE/C,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;QACpD;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,MAAM,sBAAsB,uHAAA,CAAA,sBAAmB,CAAC,IAAI;QACpD,iBAAiB;IACnB;IAEA,MAAM,wBAAwB;QAC5B,gDAAgD;QAChD,uBAAuB;QAEvB,iEAAiE;QACjE,OAAO,aAAa,CAAC,IAAI,YAAY;QAErC,IAAI,UAAU;YACZ,oBAAoB;QACtB;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,uBAAuB;QACvB,IAAI,UAAU;YACZ,oBAAoB;QACtB;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,uHAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC;QACvC;QAEA,wEAAwE;QACxE,IAAI,yBAAyB,IAAI;YAC/B,uBAAuB;QACzB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,UAAU;YACZ,oBAAoB,CAAC;QACvB,OAAO;YACL,oBAAoB,CAAC;QACvB;IACF;IAEA,MAAM,eAAe;QACnB;QACA;QACA,mBAAmB;QACnB,sBAAsB;QACtB,sBAAsB;QACtB,WAAW;QACX,YAAY;QACZ;IACF;IAEA,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,OAAO;YAAE,QAAQ;YAAS,UAAU;QAAS;;0BACnD,8OAAC,sIAAA,CAAA,UAAM;gBACL,iBAAiB;gBACjB,cAAc;gBACd,UAAU;;;;;;0BAGZ,8OAAC,kLAAA,CAAA,SAAM;gBAAC,OAAO;oBAAE,QAAQ;gBAAqB;;oBAC3C,yBACC,8OAAC,kLAAA,CAAA,SAAM;wBACL,OAAM;wBACN,WAAU;wBACV,SAAS,IAAM,oBAAoB;wBACnC,MAAM;wBACN,WAAW;4BAAE,SAAS;wBAAE;wBACxB,OAAO;kCAEP,cAAA,8OAAC,uIAAA,CAAA,UAAO;4BAAE,GAAG,YAAY;4BAAE,WAAW;;;;;;;;;;6CAGxC,8OAAC,uIAAA,CAAA,UAAO;wBAAE,GAAG,YAAY;;;;;;kCAG3B,8OAAC;wBACC,OAAO;4BACL,YAAY;4BACZ,UAAU;4BACV,SAAS;4BACT,eAAe;wBACjB;kCAEC;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/utils/openai-client.ts"], "sourcesContent": ["/**\n * OpenAI API client with configurable endpoints\n * Supports streaming responses and error handling\n */\n\nimport OpenAI from 'openai';\nimport { ApiConfigStorage } from './storage';\n\n// Types for OpenAI integration\nexport interface ChatMessage {\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n}\n\nexport interface StreamCallbacks {\n  onUpdate: (content: string) => void;\n  onSuccess: (content: string) => void;\n  onError: (error: Error) => void;\n}\n\nexport interface ChatCompletionOptions {\n  messages: ChatMessage[];\n  temperature?: number;\n  maxTokens?: number;\n  stream?: boolean;\n}\n\n/**\n * OpenAI API Client wrapper\n */\nexport class OpenAIClient {\n  private client: OpenAI | null = null;\n  private config: { apiKey: string; baseUrl: string; modelName: string } | null = null;\n\n  /**\n   * Initialize the client with stored configuration\n   */\n  async initialize(): Promise<void> {\n    try {\n      this.config = await ApiConfigStorage.load();\n      \n      if (!this.config.apiKey) {\n        throw new Error('API key not configured');\n      }\n\n      this.client = new OpenAI({\n        apiKey: this.config.apiKey,\n        baseURL: this.config.baseUrl,\n        dangerouslyAllowBrowser: true, // Note: Only for demo purposes\n      });\n    } catch (error) {\n      console.error('Failed to initialize OpenAI client:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update client configuration\n   */\n  async updateConfig(apiKey: string, baseUrl: string, modelName: string): Promise<void> {\n    await ApiConfigStorage.save({ apiKey, baseUrl, modelName });\n    await this.initialize();\n  }\n\n  /**\n   * Check if client is ready\n   */\n  isReady(): boolean {\n    return this.client !== null && this.config !== null;\n  }\n\n  /**\n   * Get current model name\n   */\n  getModelName(): string {\n    return this.config?.modelName || 'gpt-4o-mini';\n  }\n\n  /**\n   * Create a non-streaming chat completion\n   */\n  async createChatCompletion(options: ChatCompletionOptions): Promise<string> {\n    if (!this.client || !this.config) {\n      throw new Error('OpenAI client not initialized');\n    }\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.config.modelName,\n        messages: options.messages,\n        temperature: options.temperature || 0.7,\n        max_tokens: options.maxTokens || 2048,\n        stream: false,\n      });\n\n      return response.choices[0]?.message?.content || '';\n    } catch (error) {\n      console.error('Chat completion failed:', error);\n      throw new Error('Failed to get AI response');\n    }\n  }\n\n  /**\n   * Create a streaming chat completion\n   */\n  async createStreamingChatCompletion(\n    options: ChatCompletionOptions,\n    callbacks: StreamCallbacks\n  ): Promise<void> {\n    if (!this.client || !this.config) {\n      throw new Error('OpenAI client not initialized');\n    }\n\n    let content = '';\n\n    try {\n      const stream = await this.client.chat.completions.create({\n        model: this.config.modelName,\n        messages: options.messages,\n        temperature: options.temperature || 0.7,\n        max_tokens: options.maxTokens || 2048,\n        stream: true,\n      });\n\n      for await (const chunk of stream) {\n        const delta = chunk.choices[0]?.delta?.content || '';\n        if (delta) {\n          content += delta;\n          callbacks.onUpdate(content);\n        }\n      }\n\n      callbacks.onSuccess(content);\n    } catch (error) {\n      console.error('Streaming chat completion failed:', error);\n      callbacks.onError(new Error('Failed to get AI response'));\n    }\n  }\n\n  /**\n   * Validate API configuration\n   */\n  async validateConfig(apiKey: string, baseUrl: string, modelName: string): Promise<boolean> {\n    try {\n      const testClient = new OpenAI({\n        apiKey,\n        baseURL: baseUrl,\n        dangerouslyAllowBrowser: true,\n      });\n\n      // Test with a simple request\n      await testClient.chat.completions.create({\n        model: modelName,\n        messages: [{ role: 'user', content: 'Hello' }],\n        max_tokens: 5,\n      });\n\n      return true;\n    } catch (error) {\n      console.error('API validation failed:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get available models (if supported by the endpoint)\n   */\n  async getAvailableModels(): Promise<string[]> {\n    if (!this.client) {\n      throw new Error('OpenAI client not initialized');\n    }\n\n    try {\n      const response = await this.client.models.list();\n      return response.data.map(model => model.id);\n    } catch (error) {\n      console.warn('Failed to fetch models, using default list:', error);\n      // Return common model names as fallback\n      return [\n        'gpt-4o',\n        'gpt-4o-mini',\n        'gpt-4-turbo',\n        'gpt-4',\n        'gpt-3.5-turbo',\n      ];\n    }\n  }\n}\n\n// Singleton instance\nexport const openaiClient = new OpenAIClient();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;;;AAwBO,MAAM;IACH,SAAwB,KAAK;IAC7B,SAAwE,KAAK;IAErF;;GAEC,GACD,MAAM,aAA4B;QAChC,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,MAAM,uHAAA,CAAA,mBAAgB,CAAC,IAAI;YAEzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBACvB,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,MAAM,GAAG,IAAI,gJAAA,CAAA,UAAM,CAAC;gBACvB,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM;gBAC1B,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,yBAAyB;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,MAAc,EAAE,OAAe,EAAE,SAAiB,EAAiB;QACpF,MAAM,uHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC;YAAE;YAAQ;YAAS;QAAU;QACzD,MAAM,IAAI,CAAC,UAAU;IACvB;IAEA;;GAEC,GACD,UAAmB;QACjB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,KAAK;IACjD;IAEA;;GAEC,GACD,eAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,EAAE,aAAa;IACnC;IAEA;;GAEC,GACD,MAAM,qBAAqB,OAA8B,EAAmB;QAC1E,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;gBAC5B,UAAU,QAAQ,QAAQ;gBAC1B,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,QAAQ,SAAS,IAAI;gBACjC,QAAQ;YACV;YAEA,OAAO,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QAClD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,8BACJ,OAA8B,EAC9B,SAA0B,EACX;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,UAAU;QAEd,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvD,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;gBAC5B,UAAU,QAAQ,QAAQ;gBAC1B,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,QAAQ,SAAS,IAAI;gBACjC,QAAQ;YACV;YAEA,WAAW,MAAM,SAAS,OAAQ;gBAChC,MAAM,QAAQ,MAAM,OAAO,CAAC,EAAE,EAAE,OAAO,WAAW;gBAClD,IAAI,OAAO;oBACT,WAAW;oBACX,UAAU,QAAQ,CAAC;gBACrB;YACF;YAEA,UAAU,SAAS,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,UAAU,OAAO,CAAC,IAAI,MAAM;QAC9B;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,MAAc,EAAE,OAAe,EAAE,SAAiB,EAAoB;QACzF,IAAI;YACF,MAAM,aAAa,IAAI,gJAAA,CAAA,UAAM,CAAC;gBAC5B;gBACA,SAAS;gBACT,yBAAyB;YAC3B;YAEA,6BAA6B;YAC7B,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvC,OAAO;gBACP,UAAU;oBAAC;wBAAE,MAAM;wBAAQ,SAAS;oBAAQ;iBAAE;gBAC9C,YAAY;YACd;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,qBAAwC;QAC5C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI;YAC9C,OAAO,SAAS,IAAI,CAAC,GAAG,CAAC,CAAA,QAAS,MAAM,EAAE;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,+CAA+C;YAC5D,wCAAwC;YACxC,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;AACF;AAGO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/hooks/useChat.ts"], "sourcesContent": ["/**\n * React hook for managing chat functionality with streaming responses\n */\n\n'use client';\n\nimport React, { useState, useCallback, useRef } from 'react';\nimport { openaiClient, ChatMessage } from '@/utils/openai-client';\nimport { ConversationStorage, Conversation, Message } from '@/utils/storage';\n\nexport interface UseChatOptions {\n  conversationId?: string;\n  temperature?: number;\n  maxTokens?: number;\n  systemPrompt?: string;\n}\n\nexport interface UseChatReturn {\n  messages: Message[];\n  isLoading: boolean;\n  error: string | null;\n  sendMessage: (content: string) => Promise<void>;\n  clearMessages: () => void;\n  regenerateLastResponse: () => Promise<void>;\n  stopGeneration: () => void;\n}\n\n/**\n * Custom hook for chat functionality\n */\nexport function useChat(options: UseChatOptions = {}): UseChatReturn {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const abortControllerRef = useRef<AbortController | null>(null);\n  const currentResponseRef = useRef<string>('');\n\n  const {\n    conversationId,\n    temperature = 0.7,\n    maxTokens = 2048,\n    systemPrompt = 'You are a helpful AI assistant.',\n  } = options;\n\n  /**\n   * Load conversation messages\n   */\n  const loadConversation = useCallback((id: string) => {\n    const conversations = ConversationStorage.load();\n    const conversation = conversations.find(c => c.id === id);\n    if (conversation) {\n      setMessages(conversation.messages);\n    }\n  }, []);\n\n  /**\n   * Save conversation to storage\n   */\n  const saveConversation = useCallback((msgs: Message[], convId?: string) => {\n    if (msgs.length === 0) return;\n\n    // Use existing conversationId or generate a new one only if none exists\n    const targetId = conversationId || convId || `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n    const conversations = ConversationStorage.load();\n    const existingIndex = conversations.findIndex(c => c.id === targetId);\n\n    const conversation: Conversation = {\n      id: targetId,\n      title: msgs.find(m => m.role === 'user')?.content.slice(0, 50) || 'New Conversation',\n      messages: msgs,\n      createdAt: existingIndex === -1 ? Date.now() : conversations[existingIndex].createdAt,\n      updatedAt: Date.now(),\n    };\n\n    if (existingIndex === -1) {\n      ConversationStorage.addConversation(conversation);\n    } else {\n      ConversationStorage.updateConversation(targetId, conversation);\n    }\n\n    return targetId;\n  }, [conversationId]);\n\n  /**\n   * Generate unique message ID\n   */\n  const generateMessageId = (): string => {\n    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  };\n\n  /**\n   * Convert messages to OpenAI format\n   */\n  const convertToOpenAIMessages = (msgs: Message[]): ChatMessage[] => {\n    const openaiMessages: ChatMessage[] = [\n      { role: 'system', content: systemPrompt },\n    ];\n\n    msgs.forEach(msg => {\n      openaiMessages.push({\n        role: msg.role,\n        content: msg.content,\n      });\n    });\n\n    return openaiMessages;\n  };\n\n  /**\n   * Send a message and get AI response\n   */\n  const sendMessage = useCallback(async (content: string) => {\n    if (!content.trim() || isLoading) return;\n\n    setError(null);\n    setIsLoading(true);\n\n    // Create user message\n    const userMessage: Message = {\n      id: generateMessageId(),\n      content: content.trim(),\n      role: 'user',\n      timestamp: Date.now(),\n    };\n\n    // Create placeholder for assistant message\n    const assistantMessage: Message = {\n      id: generateMessageId(),\n      content: '',\n      role: 'assistant',\n      timestamp: Date.now(),\n    };\n\n    const newMessages = [...messages, userMessage, assistantMessage];\n    setMessages(newMessages);\n\n    try {\n      // Initialize OpenAI client if needed\n      if (!openaiClient.isReady()) {\n        await openaiClient.initialize();\n      }\n\n      // Create abort controller for this request\n      abortControllerRef.current = new AbortController();\n      currentResponseRef.current = '';\n\n      const openaiMessages = convertToOpenAIMessages([...messages, userMessage]);\n\n      // Start streaming response\n      await openaiClient.createStreamingChatCompletion(\n        {\n          messages: openaiMessages,\n          temperature,\n          maxTokens,\n        },\n        {\n          onUpdate: (content: string) => {\n            currentResponseRef.current = content;\n            setMessages(prev => {\n              const updated = [...prev];\n              const lastIndex = updated.length - 1;\n              if (updated[lastIndex]?.role === 'assistant') {\n                updated[lastIndex] = {\n                  ...updated[lastIndex],\n                  content,\n                };\n              }\n              return updated;\n            });\n          },\n          onSuccess: (content: string) => {\n            setMessages(prev => {\n              const updated = [...prev];\n              const lastIndex = updated.length - 1;\n              if (updated[lastIndex]?.role === 'assistant') {\n                updated[lastIndex] = {\n                  ...updated[lastIndex],\n                  content,\n                };\n              }\n\n              // Save conversation and get the conversation ID\n              const savedConversationId = saveConversation(updated);\n\n              // If this is a new conversation (no conversationId), trigger a page update\n              if (!conversationId && savedConversationId) {\n                // This will be handled by the parent component\n                window.dispatchEvent(new CustomEvent('conversationCreated', {\n                  detail: { conversationId: savedConversationId }\n                }));\n              }\n\n              return updated;\n            });\n            setIsLoading(false);\n          },\n          onError: (err: Error) => {\n            setError(err.message);\n            setIsLoading(false);\n            // Remove the placeholder assistant message on error\n            setMessages(prev => prev.slice(0, -1));\n          },\n        }\n      );\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      setIsLoading(false);\n      // Remove the placeholder assistant message on error\n      setMessages(prev => prev.slice(0, -1));\n    }\n  }, [messages, isLoading, temperature, maxTokens, systemPrompt, saveConversation]);\n\n  /**\n   * Clear all messages\n   */\n  const clearMessages = useCallback(() => {\n    setMessages([]);\n    setError(null);\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n    }\n  }, []);\n\n  /**\n   * Regenerate the last assistant response\n   */\n  const regenerateLastResponse = useCallback(async () => {\n    if (messages.length < 2 || isLoading) return;\n\n    // Find the last user message\n    const lastUserMessageIndex = messages.findLastIndex(msg => msg.role === 'user');\n    if (lastUserMessageIndex === -1) return;\n\n    const lastUserMessage = messages[lastUserMessageIndex];\n    const messagesUpToUser = messages.slice(0, lastUserMessageIndex);\n\n    setMessages(messagesUpToUser);\n    await sendMessage(lastUserMessage.content);\n  }, [messages, isLoading, sendMessage]);\n\n  /**\n   * Stop current generation\n   */\n  const stopGeneration = useCallback(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n    }\n    setIsLoading(false);\n  }, []);\n\n  // Load conversation on mount if conversationId is provided\n  React.useEffect(() => {\n    if (conversationId) {\n      loadConversation(conversationId);\n    } else {\n      // Clear messages when no conversation is selected\n      setMessages([]);\n    }\n  }, [conversationId, loadConversation]);\n\n  // Listen for new conversation events\n  React.useEffect(() => {\n    const handleNewConversation = () => {\n      clearMessages();\n    };\n\n    window.addEventListener('newConversation', handleNewConversation);\n\n    return () => {\n      window.removeEventListener('newConversation', handleNewConversation);\n    };\n  }, [clearMessages]);\n\n  return {\n    messages,\n    isLoading,\n    error,\n    sendMessage,\n    clearMessages,\n    regenerateLastResponse,\n    stopGeneration,\n  };\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAID;AACA;AACA;AAJA;;;;AA0BO,SAAS,QAAQ,UAA0B,CAAC,CAAC;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAC1D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAE1C,MAAM,EACJ,cAAc,EACd,cAAc,GAAG,EACjB,YAAY,IAAI,EAChB,eAAe,iCAAiC,EACjD,GAAG;IAEJ;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,gBAAgB,uHAAA,CAAA,sBAAmB,CAAC,IAAI;QAC9C,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,cAAc;YAChB,YAAY,aAAa,QAAQ;QACnC;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAiB;QACrD,IAAI,KAAK,MAAM,KAAK,GAAG;QAEvB,wEAAwE;QACxE,MAAM,WAAW,kBAAkB,UAAU,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAE5G,MAAM,gBAAgB,uHAAA,CAAA,sBAAmB,CAAC,IAAI;QAC9C,MAAM,gBAAgB,cAAc,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAE5D,MAAM,eAA6B;YACjC,IAAI;YACJ,OAAO,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,QAAQ,MAAM,GAAG,OAAO;YAClE,UAAU;YACV,WAAW,kBAAkB,CAAC,IAAI,KAAK,GAAG,KAAK,aAAa,CAAC,cAAc,CAAC,SAAS;YACrF,WAAW,KAAK,GAAG;QACrB;QAEA,IAAI,kBAAkB,CAAC,GAAG;YACxB,uHAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;QACtC,OAAO;YACL,uHAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC,UAAU;QACnD;QAEA,OAAO;IACT,GAAG;QAAC;KAAe;IAEnB;;GAEC,GACD,MAAM,oBAAoB;QACxB,OAAO,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACvE;IAEA;;GAEC,GACD,MAAM,0BAA0B,CAAC;QAC/B,MAAM,iBAAgC;YACpC;gBAAE,MAAM;gBAAU,SAAS;YAAa;SACzC;QAED,KAAK,OAAO,CAAC,CAAA;YACX,eAAe,IAAI,CAAC;gBAClB,MAAM,IAAI,IAAI;gBACd,SAAS,IAAI,OAAO;YACtB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI,CAAC,QAAQ,IAAI,MAAM,WAAW;QAElC,SAAS;QACT,aAAa;QAEb,sBAAsB;QACtB,MAAM,cAAuB;YAC3B,IAAI;YACJ,SAAS,QAAQ,IAAI;YACrB,MAAM;YACN,WAAW,KAAK,GAAG;QACrB;QAEA,2CAA2C;QAC3C,MAAM,mBAA4B;YAChC,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,KAAK,GAAG;QACrB;QAEA,MAAM,cAAc;eAAI;YAAU;YAAa;SAAiB;QAChE,YAAY;QAEZ,IAAI;YACF,qCAAqC;YACrC,IAAI,CAAC,gIAAA,CAAA,eAAY,CAAC,OAAO,IAAI;gBAC3B,MAAM,gIAAA,CAAA,eAAY,CAAC,UAAU;YAC/B;YAEA,2CAA2C;YAC3C,mBAAmB,OAAO,GAAG,IAAI;YACjC,mBAAmB,OAAO,GAAG;YAE7B,MAAM,iBAAiB,wBAAwB;mBAAI;gBAAU;aAAY;YAEzE,2BAA2B;YAC3B,MAAM,gIAAA,CAAA,eAAY,CAAC,6BAA6B,CAC9C;gBACE,UAAU;gBACV;gBACA;YACF,GACA;gBACE,UAAU,CAAC;oBACT,mBAAmB,OAAO,GAAG;oBAC7B,YAAY,CAAA;wBACV,MAAM,UAAU;+BAAI;yBAAK;wBACzB,MAAM,YAAY,QAAQ,MAAM,GAAG;wBACnC,IAAI,OAAO,CAAC,UAAU,EAAE,SAAS,aAAa;4BAC5C,OAAO,CAAC,UAAU,GAAG;gCACnB,GAAG,OAAO,CAAC,UAAU;gCACrB;4BACF;wBACF;wBACA,OAAO;oBACT;gBACF;gBACA,WAAW,CAAC;oBACV,YAAY,CAAA;wBACV,MAAM,UAAU;+BAAI;yBAAK;wBACzB,MAAM,YAAY,QAAQ,MAAM,GAAG;wBACnC,IAAI,OAAO,CAAC,UAAU,EAAE,SAAS,aAAa;4BAC5C,OAAO,CAAC,UAAU,GAAG;gCACnB,GAAG,OAAO,CAAC,UAAU;gCACrB;4BACF;wBACF;wBAEA,gDAAgD;wBAChD,MAAM,sBAAsB,iBAAiB;wBAE7C,2EAA2E;wBAC3E,IAAI,CAAC,kBAAkB,qBAAqB;4BAC1C,+CAA+C;4BAC/C,OAAO,aAAa,CAAC,IAAI,YAAY,uBAAuB;gCAC1D,QAAQ;oCAAE,gBAAgB;gCAAoB;4BAChD;wBACF;wBAEA,OAAO;oBACT;oBACA,aAAa;gBACf;gBACA,SAAS,CAAC;oBACR,SAAS,IAAI,OAAO;oBACpB,aAAa;oBACb,oDAAoD;oBACpD,YAAY,CAAA,OAAQ,KAAK,KAAK,CAAC,GAAG,CAAC;gBACrC;YACF;QAEJ,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,aAAa;YACb,oDAAoD;YACpD,YAAY,CAAA,OAAQ,KAAK,KAAK,CAAC,GAAG,CAAC;QACrC;IACF,GAAG;QAAC;QAAU;QAAW;QAAa;QAAW;QAAc;KAAiB;IAEhF;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,YAAY,EAAE;QACd,SAAS;QACT,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,KAAK;QAClC;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,IAAI,SAAS,MAAM,GAAG,KAAK,WAAW;QAEtC,6BAA6B;QAC7B,MAAM,uBAAuB,SAAS,aAAa,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACxE,IAAI,yBAAyB,CAAC,GAAG;QAEjC,MAAM,kBAAkB,QAAQ,CAAC,qBAAqB;QACtD,MAAM,mBAAmB,SAAS,KAAK,CAAC,GAAG;QAE3C,YAAY;QACZ,MAAM,YAAY,gBAAgB,OAAO;IAC3C,GAAG;QAAC;QAAU;QAAW;KAAY;IAErC;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,KAAK;QAClC;QACA,aAAa;IACf,GAAG,EAAE;IAEL,2DAA2D;IAC3D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,gBAAgB;YAClB,iBAAiB;QACnB,OAAO;YACL,kDAAkD;YAClD,YAAY,EAAE;QAChB;IACF,GAAG;QAAC;QAAgB;KAAiB;IAErC,qCAAqC;IACrC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,MAAM,wBAAwB;YAC5B;QACF;QAEA,OAAO,gBAAgB,CAAC,mBAAmB;QAE3C,OAAO;YACL,OAAO,mBAAmB,CAAC,mBAAmB;QAChD;IACF,GAAG;QAAC;KAAc;IAElB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/components/chat/ChatInterface.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef } from 'react';\nimport { Bubble, Sender } from '@ant-design/x';\nimport { Empty, Spin, Alert } from 'antd';\nimport { useChat } from '@/hooks/useChat';\n\ninterface ChatInterfaceProps {\n  conversationId?: string;\n}\n\nexport default function ChatInterface({ conversationId }: ChatInterfaceProps) {\n  const { messages, isLoading, error, sendMessage } = useChat({\n    conversationId,\n  });\n  const [inputValue, setInputValue] = useState('');\n  const senderRef = useRef<any>(null);\n\n  const handleSendMessage = async (content: string) => {\n    if (!content.trim()) return;\n\n    // Clear the input immediately\n    setInputValue('');\n\n    // Also clear the Sender component's internal state\n    if (senderRef.current) {\n      senderRef.current.clear?.();\n    }\n\n    await sendMessage(content);\n  };\n\n  // Convert messages to Bubble.List format\n  const bubbleItems = messages.map((message) => ({\n    key: message.id,\n    content: message.content,\n    placement: message.role === 'user' ? ('end' as const) : ('start' as const),\n    avatar: message.role === 'user' ? { style: { backgroundColor: '#1677ff' } } : undefined,\n    loading: message.role === 'assistant' && isLoading && message === messages[messages.length - 1],\n  }));\n\n  return (\n    <div\n      style={{\n        height: '100%',\n        display: 'flex',\n        flexDirection: 'column',\n        padding: '24px',\n        gap: '16px',\n      }}\n    >\n      {/* Chat Messages Area */}\n      <div\n        style={{\n          flex: 1,\n          overflow: 'auto',\n          padding: '16px',\n          border: '1px solid #f0f0f0',\n          borderRadius: '8px',\n          backgroundColor: '#fafafa',\n        }}\n      >\n        {messages.length === 0 ? (\n          <div\n            style={{\n              height: '100%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          >\n            <Empty\n              description=\"Start a conversation\"\n              image={Empty.PRESENTED_IMAGE_SIMPLE}\n            />\n          </div>\n        ) : (\n          <Bubble.List\n            items={bubbleItems}\n            style={{ height: '100%' }}\n          />\n        )}\n\n        {isLoading && messages.length > 0 && (\n          <div style={{ textAlign: 'center', padding: '16px' }}>\n            <Spin size=\"small\" />\n          </div>\n        )}\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <Alert\n          message=\"Error\"\n          description={error}\n          type=\"error\"\n          closable\n          style={{ marginBottom: '16px' }}\n        />\n      )}\n\n      {/* Message Input */}\n      <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '16px' }}>\n        <Sender\n          ref={senderRef}\n          placeholder=\"Type your message here...\"\n          value={inputValue}\n          onChange={setInputValue}\n          onSubmit={handleSendMessage}\n          loading={isLoading}\n          style={{\n            borderRadius: '8px',\n          }}\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAWe,SAAS,cAAc,EAAE,cAAc,EAAsB;IAC1E,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE;QAC1D;IACF;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAE9B,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,8BAA8B;QAC9B,cAAc;QAEd,mDAAmD;QACnD,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,KAAK;QACzB;QAEA,MAAM,YAAY;IACpB;IAEA,yCAAyC;IACzC,MAAM,cAAc,SAAS,GAAG,CAAC,CAAC,UAAY,CAAC;YAC7C,KAAK,QAAQ,EAAE;YACf,SAAS,QAAQ,OAAO;YACxB,WAAW,QAAQ,IAAI,KAAK,SAAU,QAAmB;YACzD,QAAQ,QAAQ,IAAI,KAAK,SAAS;gBAAE,OAAO;oBAAE,iBAAiB;gBAAU;YAAE,IAAI;YAC9E,SAAS,QAAQ,IAAI,KAAK,eAAe,aAAa,YAAY,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;QACjG,CAAC;IAED,qBACE,8OAAC;QACC,OAAO;YACL,QAAQ;YACR,SAAS;YACT,eAAe;YACf,SAAS;YACT,KAAK;QACP;;0BAGA,8OAAC;gBACC,OAAO;oBACL,MAAM;oBACN,UAAU;oBACV,SAAS;oBACT,QAAQ;oBACR,cAAc;oBACd,iBAAiB;gBACnB;;oBAEC,SAAS,MAAM,KAAK,kBACnB,8OAAC;wBACC,OAAO;4BACL,QAAQ;4BACR,SAAS;4BACT,YAAY;4BACZ,gBAAgB;wBAClB;kCAEA,cAAA,8OAAC,gLAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,OAAO,gLAAA,CAAA,QAAK,CAAC,sBAAsB;;;;;;;;;;6CAIvC,8OAAC,kMAAA,CAAA,SAAM,CAAC,IAAI;wBACV,OAAO;wBACP,OAAO;4BAAE,QAAQ;wBAAO;;;;;;oBAI3B,aAAa,SAAS,MAAM,GAAG,mBAC9B,8OAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAO;kCACjD,cAAA,8OAAC,8KAAA,CAAA,OAAI;4BAAC,MAAK;;;;;;;;;;;;;;;;;YAMhB,uBACC,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAa;gBACb,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAO;;;;;;0BAKlC,8OAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAqB,YAAY;gBAAO;0BAC/D,cAAA,8OAAC,kMAAA,CAAA,SAAM;oBACL,KAAK;oBACL,aAAY;oBACZ,OAAO;oBACP,UAAU;oBACV,UAAU;oBACV,SAAS;oBACT,OAAO;wBACL,cAAc;oBAChB;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/components/settings/SettingsModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Modal,\n  Form,\n  Input,\n  InputNumber,\n  Button,\n  Tabs,\n  Space,\n  Alert,\n  Typography,\n  Divider,\n  Switch,\n  Select,\n  App,\n} from 'antd';\nimport { ApiConfigStorage, SettingsStorage, PreferencesStorage } from '@/utils/storage';\nimport { openaiClient } from '@/utils/openai-client';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\ninterface SettingsModalProps {\n  open: boolean;\n  onClose: () => void;\n}\n\nexport default function SettingsModal({ open, onClose }: SettingsModalProps) {\n  const { message, modal } = App.useApp();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [testingConnection, setTestingConnection] = useState(false);\n  const [activeTab, setActiveTab] = useState('api');\n\n  // Load current settings when modal opens\n  useEffect(() => {\n    if (open) {\n      loadSettings();\n    }\n  }, [open]);\n\n  const loadSettings = async () => {\n    try {\n      const [apiConfig, appSettings, userPreferences] = await Promise.all([\n        ApiConfigStorage.load(),\n        SettingsStorage.load(),\n        PreferencesStorage.load(),\n      ]);\n\n      form.setFieldsValue({\n        // API Configuration\n        apiKey: apiConfig.apiKey,\n        baseUrl: apiConfig.baseUrl,\n        modelName: apiConfig.modelName,\n\n        // App Settings\n        temperature: appSettings.temperature,\n        maxTokens: appSettings.maxTokens,\n        systemPrompt: appSettings.systemPrompt,\n        theme: appSettings.theme,\n\n        // User Preferences\n        language: userPreferences.language,\n        autoSave: userPreferences.autoSave,\n        showTimestamps: userPreferences.showTimestamps,\n      });\n    } catch (error) {\n      console.error('Failed to load settings:', error);\n      message.error('Failed to load settings');\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      const values = await form.validateFields();\n\n      // Save API configuration\n      await ApiConfigStorage.save({\n        apiKey: values.apiKey,\n        baseUrl: values.baseUrl,\n        modelName: values.modelName,\n      });\n\n      // Save app settings\n      SettingsStorage.save({\n        temperature: values.temperature,\n        maxTokens: values.maxTokens,\n        systemPrompt: values.systemPrompt,\n        theme: values.theme,\n      });\n\n      // Save user preferences\n      PreferencesStorage.save({\n        language: values.language,\n        autoSave: values.autoSave,\n        showTimestamps: values.showTimestamps,\n      });\n\n      // Update OpenAI client\n      await openaiClient.updateConfig(\n        values.apiKey,\n        values.baseUrl,\n        values.modelName\n      );\n\n      message.success('Settings saved successfully');\n      onClose();\n    } catch (error) {\n      console.error('Failed to save settings:', error);\n      message.error('Failed to save settings');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleTestConnection = async () => {\n    try {\n      setTestingConnection(true);\n      const values = await form.validateFields(['apiKey', 'baseUrl', 'modelName']);\n\n      const isValid = await openaiClient.validateConfig(\n        values.apiKey,\n        values.baseUrl,\n        values.modelName\n      );\n\n      if (isValid) {\n        message.success('Connection test successful!');\n      } else {\n        message.error('Connection test failed. Please check your configuration.');\n      }\n    } catch (error) {\n      console.error('Connection test failed:', error);\n      message.error('Connection test failed. Please check your configuration.');\n    } finally {\n      setTestingConnection(false);\n    }\n  };\n\n  const handleReset = () => {\n    modal.confirm({\n      title: 'Reset Settings',\n      content: 'Are you sure you want to reset all settings to default values?',\n      onOk: () => {\n        form.resetFields();\n        message.info('Settings reset to default values');\n      },\n    });\n  };\n\n  const apiConfigTab = (\n    <div>\n      <Title level={4}>OpenAI API Configuration</Title>\n      <Alert\n        message=\"Security Notice\"\n        description=\"Your API key is encrypted and stored locally in your browser. It never leaves your device.\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: 24 }}\n      />\n\n      <Form.Item\n        name=\"apiKey\"\n        label=\"API Key\"\n        rules={[{ required: true, message: 'Please enter your OpenAI API key' }]}\n      >\n        <Input.Password\n          placeholder=\"sk-...\"\n          style={{ fontFamily: 'monospace' }}\n        />\n      </Form.Item>\n\n      <Form.Item\n        name=\"baseUrl\"\n        label=\"Base URL\"\n        rules={[{ required: true, message: 'Please enter the API base URL' }]}\n      >\n        <Input placeholder=\"https://api.openai.com/v1\" />\n      </Form.Item>\n\n      <Form.Item\n        name=\"modelName\"\n        label=\"Model\"\n        rules={[{ required: true, message: 'Please select a model' }]}\n      >\n        <Select\n          placeholder=\"Select a model\"\n          options={[\n            { label: 'GPT-4o', value: 'gpt-4o' },\n            { label: 'GPT-4o Mini', value: 'gpt-4o-mini' },\n            { label: 'GPT-4 Turbo', value: 'gpt-4-turbo' },\n            { label: 'GPT-4', value: 'gpt-4' },\n            { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },\n          ]}\n        />\n      </Form.Item>\n\n      <Space>\n        <Button\n          type=\"default\"\n          onClick={handleTestConnection}\n          loading={testingConnection}\n        >\n          Test Connection\n        </Button>\n      </Space>\n    </div>\n  );\n\n  const modelSettingsTab = (\n    <div>\n      <Title level={4}>Model Parameters</Title>\n\n      <Form.Item\n        name=\"temperature\"\n        label=\"Temperature\"\n        tooltip=\"Controls randomness. Lower values make responses more focused and deterministic.\"\n      >\n        <InputNumber\n          min={0}\n          max={2}\n          step={0.1}\n          style={{ width: '100%' }}\n          placeholder=\"0.7\"\n        />\n      </Form.Item>\n\n      <Form.Item\n        name=\"maxTokens\"\n        label=\"Max Tokens\"\n        tooltip=\"Maximum number of tokens in the response.\"\n      >\n        <InputNumber\n          min={1}\n          max={4096}\n          style={{ width: '100%' }}\n          placeholder=\"2048\"\n        />\n      </Form.Item>\n\n      <Form.Item\n        name=\"systemPrompt\"\n        label=\"System Prompt\"\n        tooltip=\"Instructions that guide the AI's behavior and personality.\"\n      >\n        <TextArea\n          rows={4}\n          placeholder=\"You are a helpful AI assistant.\"\n        />\n      </Form.Item>\n    </div>\n  );\n\n  const preferencesTab = (\n    <div>\n      <Title level={4}>User Preferences</Title>\n\n      <Form.Item\n        name=\"theme\"\n        label=\"Theme\"\n      >\n        <Select\n          options={[\n            { label: 'Auto', value: 'auto' },\n            { label: 'Light', value: 'light' },\n            { label: 'Dark', value: 'dark' },\n          ]}\n        />\n      </Form.Item>\n\n      <Form.Item\n        name=\"language\"\n        label=\"Language\"\n      >\n        <Select\n          options={[\n            { label: 'English', value: 'en' },\n            { label: '中文', value: 'zh' },\n          ]}\n        />\n      </Form.Item>\n\n      <Form.Item\n        name=\"autoSave\"\n        label=\"Auto Save Conversations\"\n        valuePropName=\"checked\"\n      >\n        <Switch />\n      </Form.Item>\n\n      <Form.Item\n        name=\"showTimestamps\"\n        label=\"Show Message Timestamps\"\n        valuePropName=\"checked\"\n      >\n        <Switch />\n      </Form.Item>\n    </div>\n  );\n\n  const tabItems = [\n    {\n      key: 'api',\n      label: 'API Configuration',\n      children: apiConfigTab,\n    },\n    {\n      key: 'model',\n      label: 'Model Settings',\n      children: modelSettingsTab,\n    },\n    {\n      key: 'preferences',\n      label: 'Preferences',\n      children: preferencesTab,\n    },\n  ];\n\n  return (\n    <Modal\n      title=\"Settings\"\n      open={open}\n      onCancel={onClose}\n      width={700}\n      footer={\n        <Space>\n          <Button onClick={handleReset}>\n            Reset to Default\n          </Button>\n          <Button onClick={onClose}>\n            Cancel\n          </Button>\n          <Button\n            type=\"primary\"\n            onClick={handleSave}\n            loading={loading}\n          >\n            Save Settings\n          </Button>\n        </Space>\n      }\n    >\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={{\n          temperature: 0.7,\n          maxTokens: 2048,\n          systemPrompt: 'You are a helpful AI assistant.',\n          theme: 'auto',\n          language: 'en',\n          autoSave: true,\n          showTimestamps: true,\n        }}\n      >\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={tabItems}\n        />\n      </Form>\n    </Modal>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AAnBA;;;;;;AAqBA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,gLAAA,CAAA,QAAK;AAOX,SAAS,cAAc,EAAE,IAAI,EAAE,OAAO,EAAsB;IACzE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,4KAAA,CAAA,MAAG,CAAC,MAAM;IACrC,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,CAAC,WAAW,aAAa,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClE,uHAAA,CAAA,mBAAgB,CAAC,IAAI;gBACrB,uHAAA,CAAA,kBAAe,CAAC,IAAI;gBACpB,uHAAA,CAAA,qBAAkB,CAAC,IAAI;aACxB;YAED,KAAK,cAAc,CAAC;gBAClB,oBAAoB;gBACpB,QAAQ,UAAU,MAAM;gBACxB,SAAS,UAAU,OAAO;gBAC1B,WAAW,UAAU,SAAS;gBAE9B,eAAe;gBACf,aAAa,YAAY,WAAW;gBACpC,WAAW,YAAY,SAAS;gBAChC,cAAc,YAAY,YAAY;gBACtC,OAAO,YAAY,KAAK;gBAExB,mBAAmB;gBACnB,UAAU,gBAAgB,QAAQ;gBAClC,UAAU,gBAAgB,QAAQ;gBAClC,gBAAgB,gBAAgB,cAAc;YAChD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,yBAAyB;YACzB,MAAM,uHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC;gBAC1B,QAAQ,OAAO,MAAM;gBACrB,SAAS,OAAO,OAAO;gBACvB,WAAW,OAAO,SAAS;YAC7B;YAEA,oBAAoB;YACpB,uHAAA,CAAA,kBAAe,CAAC,IAAI,CAAC;gBACnB,aAAa,OAAO,WAAW;gBAC/B,WAAW,OAAO,SAAS;gBAC3B,cAAc,OAAO,YAAY;gBACjC,OAAO,OAAO,KAAK;YACrB;YAEA,wBAAwB;YACxB,uHAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC;gBACtB,UAAU,OAAO,QAAQ;gBACzB,UAAU,OAAO,QAAQ;gBACzB,gBAAgB,OAAO,cAAc;YACvC;YAEA,uBAAuB;YACvB,MAAM,gIAAA,CAAA,eAAY,CAAC,YAAY,CAC7B,OAAO,MAAM,EACb,OAAO,OAAO,EACd,OAAO,SAAS;YAGlB,QAAQ,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,qBAAqB;YACrB,MAAM,SAAS,MAAM,KAAK,cAAc,CAAC;gBAAC;gBAAU;gBAAW;aAAY;YAE3E,MAAM,UAAU,MAAM,gIAAA,CAAA,eAAY,CAAC,cAAc,CAC/C,OAAO,MAAM,EACb,OAAO,OAAO,EACd,OAAO,SAAS;YAGlB,IAAI,SAAS;gBACX,QAAQ,OAAO,CAAC;YAClB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,OAAO,CAAC;YACZ,OAAO;YACP,SAAS;YACT,MAAM;gBACJ,KAAK,WAAW;gBAChB,QAAQ,IAAI,CAAC;YACf;QACF;IACF;IAEA,MAAM,6BACJ,8OAAC;;0BACC,8OAAC;gBAAM,OAAO;0BAAG;;;;;;0BACjB,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAG;;;;;;0BAG5B,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBAAC;wBAAE,UAAU;wBAAM,SAAS;oBAAmC;iBAAE;0BAExE,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;oBACb,aAAY;oBACZ,OAAO;wBAAE,YAAY;oBAAY;;;;;;;;;;;0BAIrC,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBAAC;wBAAE,UAAU;wBAAM,SAAS;oBAAgC;iBAAE;0BAErE,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBAAC,aAAY;;;;;;;;;;;0BAGrB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,OAAO;oBAAC;wBAAE,UAAU;wBAAM,SAAS;oBAAwB;iBAAE;0BAE7D,cAAA,8OAAC,kLAAA,CAAA,SAAM;oBACL,aAAY;oBACZ,SAAS;wBACP;4BAAE,OAAO;4BAAU,OAAO;wBAAS;wBACnC;4BAAE,OAAO;4BAAe,OAAO;wBAAc;wBAC7C;4BAAE,OAAO;4BAAe,OAAO;wBAAc;wBAC7C;4BAAE,OAAO;4BAAS,OAAO;wBAAQ;wBACjC;4BAAE,OAAO;4BAAiB,OAAO;wBAAgB;qBAClD;;;;;;;;;;;0BAIL,8OAAC,gMAAA,CAAA,QAAK;0BACJ,cAAA,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAS;oBACT,SAAS;8BACV;;;;;;;;;;;;;;;;;IAOP,MAAM,iCACJ,8OAAC;;0BACC,8OAAC;gBAAM,OAAO;0BAAG;;;;;;0BAEjB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,SAAQ;0BAER,cAAA,8OAAC,gMAAA,CAAA,cAAW;oBACV,KAAK;oBACL,KAAK;oBACL,MAAM;oBACN,OAAO;wBAAE,OAAO;oBAAO;oBACvB,aAAY;;;;;;;;;;;0BAIhB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,SAAQ;0BAER,cAAA,8OAAC,gMAAA,CAAA,cAAW;oBACV,KAAK;oBACL,KAAK;oBACL,OAAO;wBAAE,OAAO;oBAAO;oBACvB,aAAY;;;;;;;;;;;0BAIhB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,SAAQ;0BAER,cAAA,8OAAC;oBACC,MAAM;oBACN,aAAY;;;;;;;;;;;;;;;;;IAMpB,MAAM,+BACJ,8OAAC;;0BACC,8OAAC;gBAAM,OAAO;0BAAG;;;;;;0BAEjB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;0BAEN,cAAA,8OAAC,kLAAA,CAAA,SAAM;oBACL,SAAS;wBACP;4BAAE,OAAO;4BAAQ,OAAO;wBAAO;wBAC/B;4BAAE,OAAO;4BAAS,OAAO;wBAAQ;wBACjC;4BAAE,OAAO;4BAAQ,OAAO;wBAAO;qBAChC;;;;;;;;;;;0BAIL,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;0BAEN,cAAA,8OAAC,kLAAA,CAAA,SAAM;oBACL,SAAS;wBACP;4BAAE,OAAO;4BAAW,OAAO;wBAAK;wBAChC;4BAAE,OAAO;4BAAM,OAAO;wBAAK;qBAC5B;;;;;;;;;;;0BAIL,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,eAAc;0BAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;;;;;;;;;;0BAGT,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gBACR,MAAK;gBACL,OAAM;gBACN,eAAc;0BAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;;;;;;;;;;;;;;;;IAKb,MAAM,WAAW;QACf;YACE,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC,gLAAA,CAAA,QAAK;QACJ,OAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,sBACE,8OAAC,gMAAA,CAAA,QAAK;;8BACJ,8OAAC,kMAAA,CAAA,SAAM;oBAAC,SAAS;8BAAa;;;;;;8BAG9B,8OAAC,kMAAA,CAAA,SAAM;oBAAC,SAAS;8BAAS;;;;;;8BAG1B,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAS;oBACT,SAAS;8BACV;;;;;;;;;;;;kBAML,cAAA,8OAAC,8KAAA,CAAA,OAAI;YACH,MAAM;YACN,QAAO;YACP,eAAe;gBACb,aAAa;gBACb,WAAW;gBACX,cAAc;gBACd,OAAO;gBACP,UAAU;gBACV,UAAU;gBACV,gBAAgB;YAClB;sBAEA,cAAA,8OAAC,8KAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}, {"offset": {"line": 2076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport ChatInterface from '@/components/chat/ChatInterface';\nimport SettingsModal from '@/components/settings/SettingsModal';\n\nexport default function Home() {\n  const [activeConversationId, setActiveConversationId] = useState<string | undefined>();\n  const [settingsVisible, setSettingsVisible] = useState(false);\n\n  const handleSettingsClick = () => {\n    setSettingsVisible(true);\n  };\n\n  const handleSettingsClose = () => {\n    setSettingsVisible(false);\n  };\n\n  // Listen for new conversation creation\n  useEffect(() => {\n    const handleConversationCreated = (event: CustomEvent) => {\n      const { conversationId } = event.detail;\n      setActiveConversationId(conversationId);\n    };\n\n    window.addEventListener('conversationCreated', handleConversationCreated as EventListener);\n\n    return () => {\n      window.removeEventListener('conversationCreated', handleConversationCreated as EventListener);\n    };\n  }, []);\n\n  return (\n    <MainLayout\n      onSettingsClick={handleSettingsClick}\n      activeConversationId={activeConversationId}\n      onConversationChange={setActiveConversationId}\n    >\n      <ChatInterface conversationId={activeConversationId} />\n\n      <SettingsModal\n        open={settingsVisible}\n        onClose={handleSettingsClose}\n      />\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,sBAAsB;QAC1B,mBAAmB;IACrB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;IACrB;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,4BAA4B,CAAC;YACjC,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,MAAM;YACvC,wBAAwB;QAC1B;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAE/C,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;QACpD;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,0IAAA,CAAA,UAAU;QACT,iBAAiB;QACjB,sBAAsB;QACtB,sBAAsB;;0BAEtB,8OAAC,2IAAA,CAAA,UAAa;gBAAC,gBAAgB;;;;;;0BAE/B,8OAAC,+IAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}