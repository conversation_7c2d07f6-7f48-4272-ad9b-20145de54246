{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/_util/hooks/use-x-component-config.js"], "sourcesContent": ["import React from 'react';\nimport XProviderContext from \"../../x-provider/context\";\nconst defaultXComponentStyleConfig = {\n  classNames: {},\n  styles: {},\n  className: '',\n  style: {}\n};\nconst useXComponentConfig = component => {\n  const xProviderContext = React.useContext(XProviderContext);\n  return React.useMemo(() => ({\n    ...defaultXComponentStyleConfig,\n    ...xProviderContext[component]\n  }), [xProviderContext[component]]);\n};\nexport default useXComponentConfig;"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,MAAM,+BAA+B;IACnC,YAAY,CAAC;IACb,QAAQ,CAAC;IACT,WAAW;IACX,OAAO,CAAC;AACV;AACA,MAAM,sBAAsB,CAAA;IAC1B,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC,yKAAA,CAAA,UAAgB;IAC1D,OAAO,6JAAA,CAAA,UAAK,CAAC,OAAO;uCAAC,IAAM,CAAC;gBAC1B,GAAG,4BAA4B;gBAC/B,GAAG,gBAAgB,CAAC,UAAU;YAChC,CAAC;sCAAG;QAAC,gBAAgB,CAAC,UAAU;KAAC;AACnC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/hooks/useTypedEffect.js"], "sourcesContent": ["import useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nfunction isString(str) {\n  return typeof str === 'string';\n}\n\n/**\n * Return typed content and typing status when typing is enabled.\n * Or return content directly.\n */\nconst useTypedEffect = (content, typingEnabled, typingStep, typingInterval) => {\n  const prevContentRef = React.useRef('');\n  const [typingIndex, setTypingIndex] = React.useState(1);\n  const mergedTypingEnabled = typingEnabled && isString(content);\n\n  // Reset typing index when content changed\n  useLayoutEffect(() => {\n    if (!mergedTypingEnabled && isString(content)) {\n      setTypingIndex(content.length);\n    } else if (isString(content) && isString(prevContentRef.current) && content.indexOf(prevContentRef.current) !== 0) {\n      setTypingIndex(1);\n    }\n    prevContentRef.current = content;\n  }, [content]);\n\n  // Start typing\n  React.useEffect(() => {\n    if (mergedTypingEnabled && typingIndex < content.length) {\n      const id = setTimeout(() => {\n        setTypingIndex(prev => prev + typingStep);\n      }, typingInterval);\n      return () => {\n        clearTimeout(id);\n      };\n    }\n  }, [typingIndex, typingEnabled, content]);\n  const mergedTypingContent = mergedTypingEnabled ? content.slice(0, typingIndex) : content;\n  return [mergedTypingContent, mergedTypingEnabled && typingIndex < content.length];\n};\nexport default useTypedEffect;"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,SAAS,SAAS,GAAG;IACnB,OAAO,OAAO,QAAQ;AACxB;AAEA;;;CAGC,GACD,MAAM,iBAAiB,CAAC,SAAS,eAAe,YAAY;IAC1D,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACrD,MAAM,sBAAsB,iBAAiB,SAAS;IAEtD,0CAA0C;IAC1C,CAAA,GAAA,+JAAA,CAAA,UAAe,AAAD;0CAAE;YACd,IAAI,CAAC,uBAAuB,SAAS,UAAU;gBAC7C,eAAe,QAAQ,MAAM;YAC/B,OAAO,IAAI,SAAS,YAAY,SAAS,eAAe,OAAO,KAAK,QAAQ,OAAO,CAAC,eAAe,OAAO,MAAM,GAAG;gBACjH,eAAe;YACjB;YACA,eAAe,OAAO,GAAG;QAC3B;yCAAG;QAAC;KAAQ;IAEZ,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;oCAAE;YACd,IAAI,uBAAuB,cAAc,QAAQ,MAAM,EAAE;gBACvD,MAAM,KAAK;mDAAW;wBACpB;2DAAe,CAAA,OAAQ,OAAO;;oBAChC;kDAAG;gBACH;gDAAO;wBACL,aAAa;oBACf;;YACF;QACF;mCAAG;QAAC;QAAa;QAAe;KAAQ;IACxC,MAAM,sBAAsB,sBAAsB,QAAQ,KAAK,CAAC,GAAG,eAAe;IAClF,OAAO;QAAC;QAAqB,uBAAuB,cAAc,QAAQ,MAAM;KAAC;AACnF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/hooks/useTypingConfig.js"], "sourcesContent": ["import * as React from 'react';\nfunction useTypingConfig(typing) {\n  return React.useMemo(() => {\n    if (!typing) {\n      return [false, 0, 0, null];\n    }\n    let baseConfig = {\n      step: 1,\n      interval: 50,\n      // set default suffix is empty\n      suffix: null\n    };\n    if (typeof typing === 'object') {\n      baseConfig = {\n        ...baseConfig,\n        ...typing\n      };\n    }\n    return [true, baseConfig.step, baseConfig.interval, baseConfig.suffix];\n  }, [typing]);\n}\nexport default useTypingConfig;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,MAAM;IAC7B,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;mCAAE;YACnB,IAAI,CAAC,QAAQ;gBACX,OAAO;oBAAC;oBAAO;oBAAG;oBAAG;iBAAK;YAC5B;YACA,IAAI,aAAa;gBACf,MAAM;gBACN,UAAU;gBACV,8BAA8B;gBAC9B,QAAQ;YACV;YACA,IAAI,OAAO,WAAW,UAAU;gBAC9B,aAAa;oBACX,GAAG,UAAU;oBACb,GAAG,MAAM;gBACX;YACF;YACA,OAAO;gBAAC;gBAAM,WAAW,IAAI;gBAAE,WAAW,QAAQ;gBAAE,WAAW,MAAM;aAAC;QACxE;kCAAG;QAAC;KAAO;AACb;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/loading.js"], "sourcesContent": ["import React from 'react';\nconst Loading = ({\n  prefixCls\n}) => /*#__PURE__*/React.createElement(\"span\", {\n  className: `${prefixCls}-dot`\n}, /*#__PURE__*/React.createElement(\"i\", {\n  className: `${prefixCls}-dot-item`,\n  key: `item-${1}`\n}), /*#__PURE__*/React.createElement(\"i\", {\n  className: `${prefixCls}-dot-item`,\n  key: `item-${2}`\n}), /*#__PURE__*/React.createElement(\"i\", {\n  className: `${prefixCls}-dot-item`,\n  key: `item-${3}`\n}));\nexport default Loading;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,UAAU,CAAC,EACf,SAAS,EACV,GAAK,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;QAC7C,WAAW,GAAG,UAAU,IAAI,CAAC;IAC/B,GAAG,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK;QACvC,WAAW,GAAG,UAAU,SAAS,CAAC;QAClC,KAAK,CAAC,KAAK,EAAE,GAAG;IAClB,IAAI,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK;QACxC,WAAW,GAAG,UAAU,SAAS,CAAC;QAClC,KAAK,CAAC,KAAK,EAAE,GAAG;IAClB,IAAI,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK;QACxC,WAAW,GAAG,UAAU,SAAS,CAAC;QAClC,KAAK,CAAC,KAAK,EAAE,GAAG;IAClB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/version/version.js"], "sourcesContent": ["export default '1.3.0';"], "names": [], "mappings": ";;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/version/index.js"], "sourcesContent": ["// @ts-ignore\nimport version from \"./version\";\nexport default version;"], "names": [], "mappings": "AAAA,aAAa;;;;AACb;;uCACe,mKAAA,CAAA,UAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/theme/useToken.js"], "sourcesContent": ["import { createTheme, useCacheToken } from '@ant-design/cssinjs';\nimport { theme as antdTheme } from 'antd';\nimport { ignore, unitless } from 'antd/es/theme/useToken';\nimport formatToken from 'antd/es/theme/util/alias';\nimport React from 'react';\nimport version from \"../version\";\nconst defaultTheme = createTheme(antdTheme.defaultAlgorithm);\nconst preserve = {\n  screenXS: true,\n  screenXSMin: true,\n  screenXSMax: true,\n  screenSM: true,\n  screenSMMin: true,\n  screenSMMax: true,\n  screenMD: true,\n  screenMDMin: true,\n  screenMDMax: true,\n  screenLG: true,\n  screenLGMin: true,\n  screenLGMax: true,\n  screenXL: true,\n  screenXLMin: true,\n  screenXLMax: true,\n  screenXXL: true,\n  screenXXLMin: true\n};\nexport const getComputedToken = (originToken, overrideToken, theme) => {\n  const derivativeToken = theme.getDerivativeToken(originToken);\n  const {\n    override,\n    ...components\n  } = overrideToken;\n\n  // Merge with override\n  let mergedDerivativeToken = {\n    ...derivativeToken,\n    override\n  };\n\n  // Format if needed\n  mergedDerivativeToken = formatToken(mergedDerivativeToken);\n  if (components) {\n    Object.entries(components).forEach(([key, value]) => {\n      const {\n        theme: componentTheme,\n        ...componentTokens\n      } = value;\n      let mergedComponentToken = componentTokens;\n      if (componentTheme) {\n        mergedComponentToken = getComputedToken({\n          ...mergedDerivativeToken,\n          ...componentTokens\n        }, {\n          override: componentTokens\n        }, componentTheme);\n      }\n      mergedDerivativeToken[key] = mergedComponentToken;\n    });\n  }\n  return mergedDerivativeToken;\n};\nexport function useInternalToken() {\n  const {\n    token: rootDesignToken,\n    hashed,\n    theme = defaultTheme,\n    override,\n    cssVar\n  } = React.useContext(antdTheme._internalContext);\n  const [token, hashId, realToken] = useCacheToken(theme, [antdTheme.defaultSeed, rootDesignToken], {\n    salt: `${version}-${hashed || ''}`,\n    override,\n    getComputedToken,\n    cssVar: cssVar && {\n      prefix: cssVar.prefix,\n      key: cssVar.key,\n      unitless,\n      ignore,\n      preserve\n    }\n  });\n  return [theme, realToken, hashed ? hashId : '', token, cssVar];\n}\nexport default function useToken() {\n  const [theme, token, hashId] = useInternalToken();\n  return {\n    theme,\n    token,\n    hashId\n  };\n}"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,MAAM,eAAe,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,mLAAA,CAAA,QAAS,CAAC,gBAAgB;AAC3D,MAAM,WAAW;IACf,UAAU;IACV,aAAa;IACb,aAAa;IACb,UAAU;IACV,aAAa;IACb,aAAa;IACb,UAAU;IACV,aAAa;IACb,aAAa;IACb,UAAU;IACV,aAAa;IACb,aAAa;IACb,UAAU;IACV,aAAa;IACb,aAAa;IACb,WAAW;IACX,cAAc;AAChB;AACO,MAAM,mBAAmB,CAAC,aAAa,eAAe;IAC3D,MAAM,kBAAkB,MAAM,kBAAkB,CAAC;IACjD,MAAM,EACJ,QAAQ,EACR,GAAG,YACJ,GAAG;IAEJ,sBAAsB;IACtB,IAAI,wBAAwB;QAC1B,GAAG,eAAe;QAClB;IACF;IAEA,mBAAmB;IACnB,wBAAwB,CAAA,GAAA,uJAAA,CAAA,UAAW,AAAD,EAAE;IACpC,IAAI,YAAY;QACd,OAAO,OAAO,CAAC,YAAY,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC9C,MAAM,EACJ,OAAO,cAAc,EACrB,GAAG,iBACJ,GAAG;YACJ,IAAI,uBAAuB;YAC3B,IAAI,gBAAgB;gBAClB,uBAAuB,iBAAiB;oBACtC,GAAG,qBAAqB;oBACxB,GAAG,eAAe;gBACpB,GAAG;oBACD,UAAU;gBACZ,GAAG;YACL;YACA,qBAAqB,CAAC,IAAI,GAAG;QAC/B;IACF;IACA,OAAO;AACT;AACO,SAAS;IACd,MAAM,EACJ,OAAO,eAAe,EACtB,MAAM,EACN,QAAQ,YAAY,EACpB,QAAQ,EACR,MAAM,EACP,GAAG,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC,mLAAA,CAAA,QAAS,CAAC,gBAAgB;IAC/C,MAAM,CAAC,OAAO,QAAQ,UAAU,GAAG,CAAA,GAAA,yNAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;QAAC,mLAAA,CAAA,QAAS,CAAC,WAAW;QAAE;KAAgB,EAAE;QAChG,MAAM,GAAG,iKAAA,CAAA,UAAO,CAAC,CAAC,EAAE,UAAU,IAAI;QAClC;QACA;QACA,QAAQ,UAAU;YAChB,QAAQ,OAAO,MAAM;YACrB,KAAK,OAAO,GAAG;YACf,UAAA,kJAAA,CAAA,WAAQ;YACR,QAAA,kJAAA,CAAA,SAAM;YACN;QACF;IACF;IACA,OAAO;QAAC;QAAO;QAAW,SAAS,SAAS;QAAI;QAAO;KAAO;AAChE;AACe,SAAS;IACtB,MAAM,CAAC,OAAO,OAAO,OAAO,GAAG;IAC/B,OAAO;QACL;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/theme/genStyleUtils.js"], "sourcesContent": ["import { genStyleUtils } from '@ant-design/cssinjs-utils';\nimport { useXProviderContext } from \"../x-provider\";\nimport { useInternalToken } from \"./useToken\";\nexport const {\n  genStyleHooks,\n  genComponentStyleHook,\n  genSubStyleComponent\n} = genStyleUtils({\n  usePrefix: () => {\n    const {\n      getPrefixCls,\n      iconPrefixCls\n    } = useXProviderContext();\n    return {\n      iconPrefixCls,\n      rootPrefixCls: getPrefixCls()\n    };\n  },\n  useToken: () => {\n    const [theme, realToken, hashId, token, cssVar] = useInternalToken();\n    return {\n      theme,\n      realToken,\n      hashId,\n      token,\n      cssVar\n    };\n  },\n  useCSP: () => {\n    const {\n      csp\n    } = useXProviderContext();\n    return csp ?? {};\n  },\n  layer: {\n    name: 'antdx',\n    dependencies: ['antd']\n  }\n});"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;;;;AACO,MAAM,EACX,aAAa,EACb,qBAAqB,EACrB,oBAAoB,EACrB,GAAG,CAAA,GAAA,iOAAA,CAAA,gBAAa,AAAD,EAAE;IAChB,WAAW;QACT,MAAM,EACJ,YAAY,EACZ,aAAa,EACd,GAAG,CAAA,GAAA,4PAAA,CAAA,sBAAmB,AAAD;QACtB,OAAO;YACL;YACA,eAAe;QACjB;IACF;IACA,UAAU;QACR,MAAM,CAAC,OAAO,WAAW,QAAQ,OAAO,OAAO,GAAG,CAAA,GAAA,kKAAA,CAAA,mBAAgB,AAAD;QACjE,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IACA,QAAQ;QACN,MAAM,EACJ,GAAG,EACJ,GAAG,CAAA,GAAA,4PAAA,CAAA,sBAAmB,AAAD;QACtB,OAAO,OAAO,CAAC;IACjB;IACA,OAAO;QACL,MAAM;QACN,cAAc;YAAC;SAAO;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/style/content.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nexport const genVariantStyle = token => {\n  const {\n    componentCls,\n    paddingSM,\n    padding\n  } = token;\n  return {\n    [componentCls]: {\n      [`${componentCls}-content`]: {\n        // Shared: filled, outlined, shadow\n        '&-filled,&-outlined,&-shadow': {\n          padding: `${unit(paddingSM)} ${unit(padding)}`,\n          borderRadius: token.borderRadiusLG\n        },\n        // Filled:\n        '&-filled': {\n          backgroundColor: token.colorFillContent\n        },\n        // Outlined:\n        '&-outlined': {\n          border: `1px solid ${token.colorBorderSecondary}`\n        },\n        // Shadow:\n        '&-shadow': {\n          boxShadow: token.boxShadowTertiary\n        }\n      }\n    }\n  };\n};\nexport const genShapeStyle = token => {\n  const {\n    componentCls,\n    fontSize,\n    lineHeight,\n    paddingSM,\n    padding,\n    calc\n  } = token;\n  const halfRadius = calc(fontSize).mul(lineHeight).div(2).add(paddingSM).equal();\n  const contentCls = `${componentCls}-content`;\n  return {\n    [componentCls]: {\n      [contentCls]: {\n        // round:\n        '&-round': {\n          borderRadius: {\n            _skip_check_: true,\n            value: halfRadius\n          },\n          paddingInline: calc(padding).mul(1.25).equal()\n        }\n      },\n      // corner:\n      [`&-start ${contentCls}-corner`]: {\n        borderStartStartRadius: token.borderRadiusXS\n      },\n      [`&-end ${contentCls}-corner`]: {\n        borderStartEndRadius: token.borderRadiusXS\n      }\n    }\n  };\n};"], "names": [], "mappings": ";;;;AAAA;AAAA;;AACO,MAAM,kBAAkB,CAAA;IAC7B,MAAM,EACJ,YAAY,EACZ,SAAS,EACT,OAAO,EACR,GAAG;IACJ,OAAO;QACL,CAAC,aAAa,EAAE;YACd,CAAC,GAAG,aAAa,QAAQ,CAAC,CAAC,EAAE;gBAC3B,mCAAmC;gBACnC,gCAAgC;oBAC9B,SAAS,GAAG,CAAA,GAAA,oKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,CAAC,EAAE,CAAA,GAAA,oKAAA,CAAA,OAAI,AAAD,EAAE,UAAU;oBAC9C,cAAc,MAAM,cAAc;gBACpC;gBACA,UAAU;gBACV,YAAY;oBACV,iBAAiB,MAAM,gBAAgB;gBACzC;gBACA,YAAY;gBACZ,cAAc;oBACZ,QAAQ,CAAC,UAAU,EAAE,MAAM,oBAAoB,EAAE;gBACnD;gBACA,UAAU;gBACV,YAAY;oBACV,WAAW,MAAM,iBAAiB;gBACpC;YACF;QACF;IACF;AACF;AACO,MAAM,gBAAgB,CAAA;IAC3B,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,SAAS,EACT,OAAO,EACP,IAAI,EACL,GAAG;IACJ,MAAM,aAAa,KAAK,UAAU,GAAG,CAAC,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC,WAAW,KAAK;IAC7E,MAAM,aAAa,GAAG,aAAa,QAAQ,CAAC;IAC5C,OAAO;QACL,CAAC,aAAa,EAAE;YACd,CAAC,WAAW,EAAE;gBACZ,SAAS;gBACT,WAAW;oBACT,cAAc;wBACZ,cAAc;wBACd,OAAO;oBACT;oBACA,eAAe,KAAK,SAAS,GAAG,CAAC,MAAM,KAAK;gBAC9C;YACF;YACA,UAAU;YACV,CAAC,CAAC,QAAQ,EAAE,WAAW,OAAO,CAAC,CAAC,EAAE;gBAChC,wBAAwB,MAAM,cAAc;YAC9C;YACA,CAAC,CAAC,MAAM,EAAE,WAAW,OAAO,CAAC,CAAC,EAAE;gBAC9B,sBAAsB,MAAM,cAAc;YAC5C;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/style/list.js"], "sourcesContent": ["const genBubbleListStyle = token => {\n  const {\n    componentCls,\n    padding\n  } = token;\n  return {\n    [`${componentCls}-list`]: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: padding,\n      overflowY: 'auto',\n      '&::-webkit-scrollbar': {\n        width: 8,\n        backgroundColor: 'transparent'\n      },\n      '&::-webkit-scrollbar-thumb': {\n        backgroundColor: token.colorTextTertiary,\n        borderRadius: token.borderRadiusSM\n      },\n      // For Firefox\n      '&': {\n        scrollbarWidth: 'thin',\n        scrollbarColor: `${token.colorTextTertiary} transparent`\n      }\n    }\n  };\n};\nexport default genBubbleListStyle;"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAA;IACzB,MAAM,EACJ,YAAY,EACZ,OAAO,EACR,GAAG;IACJ,OAAO;QACL,CAAC,GAAG,aAAa,KAAK,CAAC,CAAC,EAAE;YACxB,SAAS;YACT,eAAe;YACf,KAAK;YACL,WAAW;YACX,wBAAwB;gBACtB,OAAO;gBACP,iBAAiB;YACnB;YACA,8BAA8B;gBAC5B,iBAAiB,MAAM,iBAAiB;gBACxC,cAAc,MAAM,cAAc;YACpC;YACA,cAAc;YACd,KAAK;gBACH,gBAAgB;gBAChB,gBAAgB,GAAG,MAAM,iBAAiB,CAAC,YAAY,CAAC;YAC1D;QACF;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/style/index.js"], "sourcesContent": ["import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { mergeToken } from '@ant-design/cssinjs-utils';\nimport { genStyleHooks } from \"../../theme/genStyleUtils\";\nimport { genShapeStyle, genVariantStyle } from \"./content\";\nimport genBubbleListStyle from \"./list\";\nconst loadingMove = new Keyframes('loadingMove', {\n  '0%': {\n    transform: 'translateY(0)'\n  },\n  '10%': {\n    transform: 'translateY(4px)'\n  },\n  '20%': {\n    transform: 'translateY(0)'\n  },\n  '30%': {\n    transform: 'translateY(-4px)'\n  },\n  '40%': {\n    transform: 'translateY(0)'\n  }\n});\nconst cursorBlink = new Keyframes('cursorBlink', {\n  '0%': {\n    opacity: 1\n  },\n  '50%': {\n    opacity: 0\n  },\n  '100%': {\n    opacity: 1\n  }\n});\n\n// biome-ignore lint/suspicious/noEmptyInterface: ComponentToken need to be empty by default\n\nconst genBubbleStyle = token => {\n  const {\n    componentCls,\n    fontSize,\n    lineHeight,\n    paddingSM,\n    colorText,\n    calc\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'flex',\n      columnGap: paddingSM,\n      [`&${componentCls}-end`]: {\n        justifyContent: 'end',\n        flexDirection: 'row-reverse',\n        [`& ${componentCls}-content-wrapper`]: {\n          alignItems: 'flex-end'\n        }\n      },\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      [`&${componentCls}-typing ${componentCls}-content:last-child::after`]: {\n        content: '\"|\"',\n        fontWeight: 900,\n        userSelect: 'none',\n        opacity: 1,\n        marginInlineStart: '0.1em',\n        animationName: cursorBlink,\n        animationDuration: '0.8s',\n        animationIterationCount: 'infinite',\n        animationTimingFunction: 'linear'\n      },\n      // ============================ Avatar =============================\n      [`& ${componentCls}-avatar`]: {\n        display: 'inline-flex',\n        justifyContent: 'center',\n        alignSelf: 'flex-start'\n      },\n      // ======================== Header & Footer ========================\n      [`& ${componentCls}-header, & ${componentCls}-footer`]: {\n        fontSize: fontSize,\n        lineHeight: lineHeight,\n        color: token.colorText\n      },\n      [`& ${componentCls}-header`]: {\n        marginBottom: token.paddingXXS\n      },\n      [`& ${componentCls}-footer`]: {\n        marginTop: paddingSM\n      },\n      // =========================== Content =============================\n      [`& ${componentCls}-content-wrapper`]: {\n        flex: 'auto',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'flex-start',\n        minWidth: 0,\n        maxWidth: '100%'\n      },\n      [`& ${componentCls}-content`]: {\n        position: 'relative',\n        boxSizing: 'border-box',\n        minWidth: 0,\n        maxWidth: '100%',\n        color: colorText,\n        fontSize: token.fontSize,\n        lineHeight: token.lineHeight,\n        minHeight: calc(paddingSM).mul(2).add(calc(lineHeight).mul(fontSize)).equal(),\n        wordBreak: 'break-word',\n        [`& ${componentCls}-dot`]: {\n          position: 'relative',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          columnGap: token.marginXS,\n          padding: `0 ${unit(token.paddingXXS)}`,\n          '&-item': {\n            backgroundColor: token.colorPrimary,\n            borderRadius: '100%',\n            width: 4,\n            height: 4,\n            animationName: loadingMove,\n            animationDuration: '2s',\n            animationIterationCount: 'infinite',\n            animationTimingFunction: 'linear',\n            '&:nth-child(1)': {\n              animationDelay: '0s'\n            },\n            '&:nth-child(2)': {\n              animationDelay: '0.2s'\n            },\n            '&:nth-child(3)': {\n              animationDelay: '0.4s'\n            }\n          }\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = () => ({});\nexport default genStyleHooks('Bubble', token => {\n  const bubbleToken = mergeToken(token, {});\n  return [genBubbleStyle(bubbleToken), genBubbleListStyle(bubbleToken), genVariantStyle(bubbleToken), genShapeStyle(bubbleToken)];\n}, prepareComponentToken);"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AACA,MAAM,cAAc,IAAI,wMAAA,CAAA,YAAS,CAAC,eAAe;IAC/C,MAAM;QACJ,WAAW;IACb;IACA,OAAO;QACL,WAAW;IACb;IACA,OAAO;QACL,WAAW;IACb;IACA,OAAO;QACL,WAAW;IACb;IACA,OAAO;QACL,WAAW;IACb;AACF;AACA,MAAM,cAAc,IAAI,wMAAA,CAAA,YAAS,CAAC,eAAe;IAC/C,MAAM;QACJ,SAAS;IACX;IACA,OAAO;QACL,SAAS;IACX;IACA,QAAQ;QACN,SAAS;IACX;AACF;AAEA,4FAA4F;AAE5F,MAAM,iBAAiB,CAAA;IACrB,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,SAAS,EACT,SAAS,EACT,IAAI,EACL,GAAG;IACJ,OAAO;QACL,CAAC,aAAa,EAAE;YACd,SAAS;YACT,WAAW;YACX,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC,CAAC,EAAE;gBACxB,gBAAgB;gBAChB,eAAe;gBACf,CAAC,CAAC,EAAE,EAAE,aAAa,gBAAgB,CAAC,CAAC,EAAE;oBACrC,YAAY;gBACd;YACF;YACA,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC,CAAC,EAAE;gBACxB,WAAW;YACb;YACA,CAAC,CAAC,CAAC,EAAE,aAAa,QAAQ,EAAE,aAAa,0BAA0B,CAAC,CAAC,EAAE;gBACrE,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,SAAS;gBACT,mBAAmB;gBACnB,eAAe;gBACf,mBAAmB;gBACnB,yBAAyB;gBACzB,yBAAyB;YAC3B;YACA,oEAAoE;YACpE,CAAC,CAAC,EAAE,EAAE,aAAa,OAAO,CAAC,CAAC,EAAE;gBAC5B,SAAS;gBACT,gBAAgB;gBAChB,WAAW;YACb;YACA,oEAAoE;YACpE,CAAC,CAAC,EAAE,EAAE,aAAa,WAAW,EAAE,aAAa,OAAO,CAAC,CAAC,EAAE;gBACtD,UAAU;gBACV,YAAY;gBACZ,OAAO,MAAM,SAAS;YACxB;YACA,CAAC,CAAC,EAAE,EAAE,aAAa,OAAO,CAAC,CAAC,EAAE;gBAC5B,cAAc,MAAM,UAAU;YAChC;YACA,CAAC,CAAC,EAAE,EAAE,aAAa,OAAO,CAAC,CAAC,EAAE;gBAC5B,WAAW;YACb;YACA,oEAAoE;YACpE,CAAC,CAAC,EAAE,EAAE,aAAa,gBAAgB,CAAC,CAAC,EAAE;gBACrC,MAAM;gBACN,SAAS;gBACT,eAAe;gBACf,YAAY;gBACZ,UAAU;gBACV,UAAU;YACZ;YACA,CAAC,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,CAAC,EAAE;gBAC7B,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,OAAO;gBACP,UAAU,MAAM,QAAQ;gBACxB,YAAY,MAAM,UAAU;gBAC5B,WAAW,KAAK,WAAW,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,YAAY,GAAG,CAAC,WAAW,KAAK;gBAC3E,WAAW;gBACX,CAAC,CAAC,EAAE,EAAE,aAAa,IAAI,CAAC,CAAC,EAAE;oBACzB,UAAU;oBACV,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,WAAW,MAAM,QAAQ;oBACzB,SAAS,CAAC,EAAE,EAAE,CAAA,GAAA,oKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG;oBACtC,UAAU;wBACR,iBAAiB,MAAM,YAAY;wBACnC,cAAc;wBACd,OAAO;wBACP,QAAQ;wBACR,eAAe;wBACf,mBAAmB;wBACnB,yBAAyB;wBACzB,yBAAyB;wBACzB,kBAAkB;4BAChB,gBAAgB;wBAClB;wBACA,kBAAkB;4BAChB,gBAAgB;wBAClB;wBACA,kBAAkB;4BAChB,gBAAgB;wBAClB;oBACF;gBACF;YACF;QACF;IACF;AACF;AACO,MAAM,wBAAwB,IAAM,CAAC,CAAC,CAAC;uCAC/B,CAAA,GAAA,uKAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,CAAA;IACrC,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,OAAO,CAAC;IACvC,OAAO;QAAC,eAAe;QAAc,CAAA,GAAA,wKAAA,CAAA,UAAkB,AAAD,EAAE;QAAc,CAAA,GAAA,2KAAA,CAAA,kBAAe,AAAD,EAAE;QAAc,CAAA,GAAA,2KAAA,CAAA,gBAAa,AAAD,EAAE;KAAa;AACjI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/Bubble.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classnames from 'classnames';\nimport React from 'react';\nimport { Avatar } from 'antd';\nimport useXComponentConfig from \"../_util/hooks/use-x-component-config\";\nimport { useXProviderContext } from \"../x-provider\";\nimport useTypedEffect from \"./hooks/useTypedEffect\";\nimport useTypingConfig from \"./hooks/useTypingConfig\";\nimport Loading from \"./loading\";\nimport useStyle from \"./style\";\nexport const BubbleContext = /*#__PURE__*/React.createContext({});\nconst Bubble = (props, ref) => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    classNames = {},\n    styles = {},\n    avatar,\n    placement = 'start',\n    loading = false,\n    loadingRender,\n    typing,\n    content = '',\n    messageRender,\n    variant = 'filled',\n    shape,\n    onTypingComplete,\n    header,\n    footer,\n    _key,\n    ...otherHtmlProps\n  } = props;\n  const {\n    onUpdate\n  } = React.useContext(BubbleContext);\n\n  // ============================= Refs =============================\n  const divRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: divRef.current\n  }));\n\n  // ============================ Prefix ============================\n  const {\n    direction,\n    getPrefixCls\n  } = useXProviderContext();\n  const prefixCls = getPrefixCls('bubble', customizePrefixCls);\n\n  // ===================== Component Config =========================\n  const contextConfig = useXComponentConfig('bubble');\n\n  // ============================ Typing ============================\n  const [typingEnabled, typingStep, typingInterval, customSuffix] = useTypingConfig(typing);\n  const [typedContent, isTyping] = useTypedEffect(content, typingEnabled, typingStep, typingInterval);\n  React.useEffect(() => {\n    onUpdate?.();\n  }, [typedContent]);\n  const triggerTypingCompleteRef = React.useRef(false);\n  React.useEffect(() => {\n    if (!isTyping && !loading) {\n      // StrictMode will trigger this twice,\n      // So we need a flag to avoid that\n      if (!triggerTypingCompleteRef.current) {\n        triggerTypingCompleteRef.current = true;\n        onTypingComplete?.();\n      }\n    } else {\n      triggerTypingCompleteRef.current = false;\n    }\n  }, [isTyping, loading]);\n\n  // ============================ Styles ============================\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const mergedCls = classnames(prefixCls, rootClassName, contextConfig.className, className, hashId, cssVarCls, `${prefixCls}-${placement}`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-typing`]: isTyping && !loading && !messageRender && !customSuffix\n  });\n\n  // ============================ Avatar ============================\n  const avatarNode = React.useMemo(() => /*#__PURE__*/React.isValidElement(avatar) ? avatar : /*#__PURE__*/React.createElement(Avatar, avatar), [avatar]);\n\n  // =========================== Content ============================\n  const mergedContent = React.useMemo(() => messageRender ? messageRender(typedContent) : typedContent, [typedContent, messageRender]);\n  const renderSlot = node => typeof node === 'function' ? node(typedContent, {\n    key: _key\n  }) : node;\n\n  // ============================ Render ============================\n  let contentNode;\n  if (loading) {\n    contentNode = loadingRender ? loadingRender() : /*#__PURE__*/React.createElement(Loading, {\n      prefixCls: prefixCls\n    });\n  } else {\n    contentNode = /*#__PURE__*/React.createElement(React.Fragment, null, mergedContent, isTyping && customSuffix);\n  }\n  let fullContent = /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...contextConfig.styles.content,\n      ...styles.content\n    },\n    className: classnames(`${prefixCls}-content`, `${prefixCls}-content-${variant}`, shape && `${prefixCls}-content-${shape}`, contextConfig.classNames.content, classNames.content)\n  }, contentNode);\n  if (header || footer) {\n    fullContent = /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-content-wrapper`\n    }, header && /*#__PURE__*/React.createElement(\"div\", {\n      className: classnames(`${prefixCls}-header`, contextConfig.classNames.header, classNames.header),\n      style: {\n        ...contextConfig.styles.header,\n        ...styles.header\n      }\n    }, renderSlot(header)), fullContent, footer && /*#__PURE__*/React.createElement(\"div\", {\n      className: classnames(`${prefixCls}-footer`, contextConfig.classNames.footer, classNames.footer),\n      style: {\n        ...contextConfig.styles.footer,\n        ...styles.footer\n      }\n    }, renderSlot(footer)));\n  }\n  return wrapCSSVar( /*#__PURE__*/React.createElement(\"div\", _extends({\n    style: {\n      ...contextConfig.style,\n      ...style\n    },\n    className: mergedCls\n  }, otherHtmlProps, {\n    ref: divRef\n  }), avatar && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...contextConfig.styles.avatar,\n      ...styles.avatar\n    },\n    className: classnames(`${prefixCls}-avatar`, contextConfig.classNames.avatar, classNames.avatar)\n  }, avatarNode), fullContent));\n};\nconst ForwardBubble = /*#__PURE__*/React.forwardRef(Bubble);\nif (process.env.NODE_ENV !== 'production') {\n  ForwardBubble.displayName = 'Bubble';\n}\nexport default ForwardBubble;"], "names": [], "mappings": ";;;;AA4II;AA5IJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACO,MAAM,gBAAgB,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,CAAC;AAC/D,MAAM,SAAS,CAAC,OAAO;IACrB,MAAM,EACJ,WAAW,kBAAkB,EAC7B,SAAS,EACT,aAAa,EACb,KAAK,EACL,aAAa,CAAC,CAAC,EACf,SAAS,CAAC,CAAC,EACX,MAAM,EACN,YAAY,OAAO,EACnB,UAAU,KAAK,EACf,aAAa,EACb,MAAM,EACN,UAAU,EAAE,EACZ,aAAa,EACb,UAAU,QAAQ,EAClB,KAAK,EACL,gBAAgB,EAChB,MAAM,EACN,MAAM,EACN,IAAI,EACJ,GAAG,gBACJ,GAAG;IAC<PERSON>,MAAM,EACJ,QAAQ,EACT,GAAG,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IAErB,mEAAmE;IACnE,MAAM,SAAS,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5B,6JAAA,CAAA,UAAK,CAAC,mBAAmB,CAAC;sCAAK,IAAM,CAAC;gBACpC,eAAe,OAAO,OAAO;YAC/B,CAAC;;IAED,mEAAmE;IACnE,MAAM,EACJ,SAAS,EACT,YAAY,EACb,GAAG,CAAA,GAAA,4PAAA,CAAA,sBAAmB,AAAD;IACtB,MAAM,YAAY,aAAa,UAAU;IAEzC,mEAAmE;IACnE,MAAM,gBAAgB,CAAA,GAAA,kMAAA,CAAA,UAAmB,AAAD,EAAE;IAE1C,mEAAmE;IACnE,MAAM,CAAC,eAAe,YAAY,gBAAgB,aAAa,GAAG,CAAA,GAAA,mLAAA,CAAA,UAAe,AAAD,EAAE;IAClF,MAAM,CAAC,cAAc,SAAS,GAAG,CAAA,GAAA,kLAAA,CAAA,UAAc,AAAD,EAAE,SAAS,eAAe,YAAY;IACpF,6JAAA,CAAA,UAAK,CAAC,SAAS;4BAAC;YACd;QACF;2BAAG;QAAC;KAAa;IACjB,MAAM,2BAA2B,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9C,6JAAA,CAAA,UAAK,CAAC,SAAS;4BAAC;YACd,IAAI,CAAC,YAAY,CAAC,SAAS;gBACzB,sCAAsC;gBACtC,kCAAkC;gBAClC,IAAI,CAAC,yBAAyB,OAAO,EAAE;oBACrC,yBAAyB,OAAO,GAAG;oBACnC;gBACF;YACF,OAAO;gBACL,yBAAyB,OAAO,GAAG;YACrC;QACF;2BAAG;QAAC;QAAU;KAAQ;IAEtB,mEAAmE;IACnE,MAAM,CAAC,YAAY,QAAQ,UAAU,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAQ,AAAD,EAAE;IACjD,MAAM,YAAY,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,eAAe,cAAc,SAAS,EAAE,WAAW,QAAQ,WAAW,GAAG,UAAU,CAAC,EAAE,WAAW,EAAE;QACzI,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC,EAAE,cAAc;QACpC,CAAC,GAAG,UAAU,OAAO,CAAC,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,iBAAiB,CAAC;IACtE;IAEA,mEAAmE;IACnE,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,OAAO;sCAAC,IAAM,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,cAAc,CAAC,UAAU,SAAS,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qLAAA,CAAA,SAAM,EAAE;qCAAS;QAAC;KAAO;IAEtJ,mEAAmE;IACnE,MAAM,gBAAgB,6JAAA,CAAA,UAAK,CAAC,OAAO;yCAAC,IAAM,gBAAgB,cAAc,gBAAgB;wCAAc;QAAC;QAAc;KAAc;IACnI,MAAM,aAAa,CAAA,OAAQ,OAAO,SAAS,aAAa,KAAK,cAAc;YACzE,KAAK;QACP,KAAK;IAEL,mEAAmE;IACnE,IAAI;IACJ,IAAI,SAAS;QACX,cAAc,gBAAgB,kBAAkB,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,kKAAA,CAAA,UAAO,EAAE;YACxF,WAAW;QACb;IACF,OAAO;QACL,cAAc,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,eAAe,YAAY;IAClG;IACA,IAAI,cAAc,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACxD,OAAO;YACL,GAAG,cAAc,MAAM,CAAC,OAAO;YAC/B,GAAG,OAAO,OAAO;QACnB;QACA,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,UAAU,QAAQ,CAAC,EAAE,GAAG,UAAU,SAAS,EAAE,SAAS,EAAE,SAAS,GAAG,UAAU,SAAS,EAAE,OAAO,EAAE,cAAc,UAAU,CAAC,OAAO,EAAE,WAAW,OAAO;IACjL,GAAG;IACH,IAAI,UAAU,QAAQ;QACpB,cAAc,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;YACpD,WAAW,GAAG,UAAU,gBAAgB,CAAC;QAC3C,GAAG,UAAU,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;YACnD,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,UAAU,OAAO,CAAC,EAAE,cAAc,UAAU,CAAC,MAAM,EAAE,WAAW,MAAM;YAC/F,OAAO;gBACL,GAAG,cAAc,MAAM,CAAC,MAAM;gBAC9B,GAAG,OAAO,MAAM;YAClB;QACF,GAAG,WAAW,UAAU,aAAa,UAAU,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;YACrF,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,UAAU,OAAO,CAAC,EAAE,cAAc,UAAU,CAAC,MAAM,EAAE,WAAW,MAAM;YAC/F,OAAO;gBACL,GAAG,cAAc,MAAM,CAAC,MAAM;gBAC9B,GAAG,OAAO,MAAM;YAClB;QACF,GAAG,WAAW;IAChB;IACA,OAAO,WAAY,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAClE,OAAO;YACL,GAAG,cAAc,KAAK;YACtB,GAAG,KAAK;QACV;QACA,WAAW;IACb,GAAG,gBAAgB;QACjB,KAAK;IACP,IAAI,UAAU,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACpD,OAAO;YACL,GAAG,cAAc,MAAM,CAAC,MAAM;YAC9B,GAAG,OAAO,MAAM;QAClB;QACA,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,UAAU,OAAO,CAAC,EAAE,cAAc,UAAU,CAAC,MAAM,EAAE,WAAW,MAAM;IACjG,GAAG,aAAa;AAClB;AACA,MAAM,gBAAgB,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC;AACpD,wCAA2C;IACzC,cAAc,WAAW,GAAG;AAC9B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/hooks/useListData.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useListData(items, roles) {\n  const getRoleBubbleProps = React.useCallback((bubble, index) => {\n    if (typeof roles === 'function') {\n      return roles(bubble, index);\n    }\n    if (roles) {\n      return roles[bubble.role] || {};\n    }\n    return {};\n  }, [roles]);\n  return React.useMemo(() => (items || []).map((bubbleData, i) => {\n    const mergedKey = bubbleData.key ?? `preset_${i}`;\n    return {\n      ...getRoleBubbleProps(bubbleData, i),\n      ...bubbleData,\n      key: mergedKey\n    };\n  }), [items, getRoleBubbleProps]);\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,YAAY,KAAK,EAAE,KAAK;IAC9C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;uDAAE,CAAC,QAAQ;YACpD,IAAI,OAAO,UAAU,YAAY;gBAC/B,OAAO,MAAM,QAAQ;YACvB;YACA,IAAI,OAAO;gBACT,OAAO,KAAK,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC;YAChC;YACA,OAAO,CAAC;QACV;sDAAG;QAAC;KAAM;IACV,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;+BAAE,IAAM,CAAC,SAAS,EAAE,EAAE,GAAG;uCAAC,CAAC,YAAY;oBACxD,MAAM,YAAY,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG;oBACjD,OAAO;wBACL,GAAG,mBAAmB,YAAY,EAAE;wBACpC,GAAG,UAAU;wBACb,KAAK;oBACP;gBACF;;8BAAI;QAAC;QAAO;KAAmB;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/BubbleList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport { useEvent } from 'rc-util';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\nimport { useXProviderContext } from \"../x-provider\";\nimport Bubble, { BubbleContext } from \"./Bubble\";\nimport useListData from \"./hooks/useListData\";\nimport useStyle from \"./style\";\nconst BubbleListItem = ({\n  _key,\n  ...restProps\n}, ref) => /*#__PURE__*/React.createElement(Bubble, _extends({}, restProps, {\n  ref: node => {\n    if (node) {\n      ref.current[_key] = node;\n    } else {\n      delete ref.current?.[_key];\n    }\n  }\n}));\nconst MemoBubbleListItem = /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(BubbleListItem));\nconst TOLERANCE = 1;\nconst BubbleList = (props, ref) => {\n  const {\n    prefixCls: customizePrefixCls,\n    rootClassName,\n    className,\n    items,\n    autoScroll = true,\n    roles,\n    ...restProps\n  } = props;\n  const domProps = pickAttrs(restProps, {\n    attr: true,\n    aria: true\n  });\n\n  // ============================= Refs =============================\n  const listRef = React.useRef(null);\n  const bubbleRefs = React.useRef({});\n\n  // ============================ Prefix ============================\n  const {\n    getPrefixCls\n  } = useXProviderContext();\n  const prefixCls = getPrefixCls('bubble', customizePrefixCls);\n  const listPrefixCls = `${prefixCls}-list`;\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n\n  // ============================ Typing ============================\n  const [initialized, setInitialized] = React.useState(false);\n  React.useEffect(() => {\n    setInitialized(true);\n    return () => {\n      setInitialized(false);\n    };\n  }, []);\n\n  // ============================= Data =============================\n  const mergedData = useListData(items, roles);\n\n  // ============================ Scroll ============================\n  // Is current scrollTop at the end. User scroll will make this false.\n  const [scrollReachEnd, setScrollReachEnd] = React.useState(true);\n  const [updateCount, setUpdateCount] = React.useState(0);\n  const onInternalScroll = e => {\n    const target = e.target;\n    setScrollReachEnd(target.scrollHeight - Math.abs(target.scrollTop) - target.clientHeight <= TOLERANCE);\n  };\n  React.useEffect(() => {\n    if (autoScroll && listRef.current && scrollReachEnd) {\n      listRef.current.scrollTo({\n        top: listRef.current.scrollHeight\n      });\n    }\n  }, [updateCount]);\n\n  // Always scroll to bottom when data change\n  React.useEffect(() => {\n    if (autoScroll) {\n      // New date come, the origin last one is the second last one\n      const lastItemKey = mergedData[mergedData.length - 2]?.key;\n      const bubbleInst = bubbleRefs.current[lastItemKey];\n\n      // Auto scroll if last 2 item is visible\n      if (bubbleInst) {\n        const {\n          nativeElement\n        } = bubbleInst;\n        const {\n          top,\n          bottom\n        } = nativeElement.getBoundingClientRect();\n        const {\n          top: listTop,\n          bottom: listBottom\n        } = listRef.current.getBoundingClientRect();\n        const isVisible = top < listBottom && bottom > listTop;\n        if (isVisible) {\n          setUpdateCount(c => c + 1);\n          setScrollReachEnd(true);\n        }\n      }\n    }\n  }, [mergedData.length]);\n\n  // ========================== Outer Ref ===========================\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: listRef.current,\n    scrollTo: ({\n      key,\n      offset,\n      behavior = 'smooth',\n      block\n    }) => {\n      if (typeof offset === 'number') {\n        // Offset scroll\n        listRef.current.scrollTo({\n          top: offset,\n          behavior\n        });\n      } else if (key !== undefined) {\n        // Key scroll\n        const bubbleInst = bubbleRefs.current[key];\n        if (bubbleInst) {\n          // Block current auto scrolling\n          const index = mergedData.findIndex(dataItem => dataItem.key === key);\n          setScrollReachEnd(index === mergedData.length - 1);\n\n          // Do native scroll\n          bubbleInst.nativeElement.scrollIntoView({\n            behavior,\n            block\n          });\n        }\n      }\n    }\n  }));\n\n  // =========================== Context ============================\n  // When bubble content update, we try to trigger `autoScroll` for sync\n  const onBubbleUpdate = useEvent(() => {\n    if (autoScroll) {\n      setUpdateCount(c => c + 1);\n    }\n  });\n  const context = React.useMemo(() => ({\n    onUpdate: onBubbleUpdate\n  }), []);\n\n  // ============================ Render ============================\n  return wrapCSSVar( /*#__PURE__*/React.createElement(BubbleContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({}, domProps, {\n    className: classNames(listPrefixCls, rootClassName, className, hashId, cssVarCls, {\n      [`${listPrefixCls}-reach-end`]: scrollReachEnd\n    }),\n    ref: listRef,\n    onScroll: onInternalScroll\n  }), mergedData.map(({\n    key,\n    ...bubble\n  }) => /*#__PURE__*/React.createElement(MemoBubbleListItem, _extends({}, bubble, {\n    key: key,\n    _key: key,\n    ref: bubbleRefs,\n    typing: initialized ? bubble.typing : false\n  }))))));\n};\nconst ForwardBubbleList = /*#__PURE__*/React.forwardRef(BubbleList);\nif (process.env.NODE_ENV !== 'production') {\n  ForwardBubbleList.displayName = 'BubbleList';\n}\nexport default ForwardBubbleList;"], "names": [], "mappings": ";;;AA2KI;AA3KJ;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACA,MAAM,iBAAiB,CAAC,EACtB,IAAI,EACJ,GAAG,WACJ,EAAE,MAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,iKAAA,CAAA,UAAM,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,WAAW;QAC1E,KAAK,CAAA;YACH,IAAI,MAAM;gBACR,IAAI,OAAO,CAAC,KAAK,GAAG;YACtB,OAAO;gBACL,OAAO,IAAI,OAAO,EAAE,CAAC,KAAK;YAC5B;QACF;IACF;AACA,MAAM,qBAAqB,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,OAAU,AAAD,EAAG,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;AAClF,MAAM,YAAY;AAClB,MAAM,aAAa,CAAC,OAAO;IACzB,MAAM,EACJ,WAAW,kBAAkB,EAC7B,aAAa,EACb,SAAS,EACT,KAAK,EACL,aAAa,IAAI,EACjB,KAAK,EACL,GAAG,WACJ,GAAG;IACJ,MAAM,WAAW,CAAA,GAAA,gJAAA,CAAA,UAAS,AAAD,EAAE,WAAW;QACpC,MAAM;QACN,MAAM;IACR;IAEA,mEAAmE;IACnE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE,CAAC;IAEjC,mEAAmE;IACnE,MAAM,EACJ,YAAY,EACb,GAAG,CAAA,GAAA,4PAAA,CAAA,sBAAmB,AAAD;IACtB,MAAM,YAAY,aAAa,UAAU;IACzC,MAAM,gBAAgB,GAAG,UAAU,KAAK,CAAC;IACzC,MAAM,CAAC,YAAY,QAAQ,UAAU,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAQ,AAAD,EAAE;IAEjD,mEAAmE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;gCAAE;YACd,eAAe;YACf;wCAAO;oBACL,eAAe;gBACjB;;QACF;+BAAG,EAAE;IAEL,mEAAmE;IACnE,MAAM,aAAa,CAAA,GAAA,+KAAA,CAAA,UAAW,AAAD,EAAE,OAAO;IAEtC,mEAAmE;IACnE,qEAAqE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACrD,MAAM,mBAAmB,CAAA;QACvB,MAAM,SAAS,EAAE,MAAM;QACvB,kBAAkB,OAAO,YAAY,GAAG,KAAK,GAAG,CAAC,OAAO,SAAS,IAAI,OAAO,YAAY,IAAI;IAC9F;IACA,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;gCAAE;YACd,IAAI,cAAc,QAAQ,OAAO,IAAI,gBAAgB;gBACnD,QAAQ,OAAO,CAAC,QAAQ,CAAC;oBACvB,KAAK,QAAQ,OAAO,CAAC,YAAY;gBACnC;YACF;QACF;+BAAG;QAAC;KAAY;IAEhB,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;gCAAE;YACd,IAAI,YAAY;gBACd,4DAA4D;gBAC5D,MAAM,cAAc,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,EAAE;gBACvD,MAAM,aAAa,WAAW,OAAO,CAAC,YAAY;gBAElD,wCAAwC;gBACxC,IAAI,YAAY;oBACd,MAAM,EACJ,aAAa,EACd,GAAG;oBACJ,MAAM,EACJ,GAAG,EACH,MAAM,EACP,GAAG,cAAc,qBAAqB;oBACvC,MAAM,EACJ,KAAK,OAAO,EACZ,QAAQ,UAAU,EACnB,GAAG,QAAQ,OAAO,CAAC,qBAAqB;oBACzC,MAAM,YAAY,MAAM,cAAc,SAAS;oBAC/C,IAAI,WAAW;wBACb;oDAAe,CAAA,IAAK,IAAI;;wBACxB,kBAAkB;oBACpB;gBACF;YACF;QACF;+BAAG;QAAC,WAAW,MAAM;KAAC;IAEtB,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,sBAAyB,AAAD,EAAE;0CAAK,IAAM,CAAC;gBACpC,eAAe,QAAQ,OAAO;gBAC9B,QAAQ;sDAAE,CAAC,EACT,GAAG,EACH,MAAM,EACN,WAAW,QAAQ,EACnB,KAAK,EACN;wBACC,IAAI,OAAO,WAAW,UAAU;4BAC9B,gBAAgB;4BAChB,QAAQ,OAAO,CAAC,QAAQ,CAAC;gCACvB,KAAK;gCACL;4BACF;wBACF,OAAO,IAAI,QAAQ,WAAW;4BAC5B,aAAa;4BACb,MAAM,aAAa,WAAW,OAAO,CAAC,IAAI;4BAC1C,IAAI,YAAY;gCACd,+BAA+B;gCAC/B,MAAM,QAAQ,WAAW,SAAS;4EAAC,CAAA,WAAY,SAAS,GAAG,KAAK;;gCAChE,kBAAkB,UAAU,WAAW,MAAM,GAAG;gCAEhD,mBAAmB;gCACnB,WAAW,aAAa,CAAC,cAAc,CAAC;oCACtC;oCACA;gCACF;4BACF;wBACF;oBACF;;YACF,CAAC;;IAED,mEAAmE;IACnE,sEAAsE;IACtE,MAAM,iBAAiB,CAAA,GAAA,+LAAA,CAAA,WAAQ,AAAD;+CAAE;YAC9B,IAAI,YAAY;gBACd;2DAAe,CAAA,IAAK,IAAI;;YAC1B;QACF;;IACA,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;uCAAE,IAAM,CAAC;gBACnC,UAAU;YACZ,CAAC;sCAAG,EAAE;IAEN,mEAAmE;IACnE,OAAO,WAAY,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,iKAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE;QAC1E,OAAO;IACT,GAAG,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,UAAU;QAChE,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,eAAe,eAAe,WAAW,QAAQ,WAAW;YAChF,CAAC,GAAG,cAAc,UAAU,CAAC,CAAC,EAAE;QAClC;QACA,KAAK;QACL,UAAU;IACZ,IAAI,WAAW,GAAG,CAAC,CAAC,EAClB,GAAG,EACH,GAAG,QACJ,GAAK,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,oBAAoB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,QAAQ;YAC9E,KAAK;YACL,MAAM;YACN,KAAK;YACL,QAAQ,cAAc,OAAO,MAAM,GAAG;QACxC;AACF;AACA,MAAM,oBAAoB,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;AACxD,wCAA2C;IACzC,kBAAkB,WAAW,GAAG;AAClC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/bubble/index.js"], "sourcesContent": ["import Bubble from \"./Bubble\";\nimport List from \"./BubbleList\";\nBubble.List = List;\nexport default Bubble;"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,iKAAA,CAAA,UAAM,CAAC,IAAI,GAAG,qKAAA,CAAA,UAAI;uCACH,iKAAA,CAAA,UAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/_util/hooks/use-proxy-imperative-handle.js"], "sourcesContent": ["// Proxy the dom ref with `{ nativeElement, otherFn }` type\n// ref: https://github.com/ant-design/ant-design/discussions/45242\n\nimport { useImperativeHandle } from 'react';\nexport default function useProxyImperativeHandle(ref, init) {\n  return useImperativeHandle(ref, () => {\n    const refObj = init();\n    const {\n      nativeElement\n    } = refObj;\n    return new Proxy(nativeElement, {\n      get(obj, prop) {\n        if (refObj[prop]) {\n          return refObj[prop];\n        }\n        return Reflect.get(obj, prop);\n      }\n    });\n  });\n}"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,kEAAkE;;;;AAElE;;AACe,SAAS,yBAAyB,GAAG,EAAE,IAAI;IACxD,OAAO,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;wDAAK;YAC9B,MAAM,SAAS;YACf,MAAM,EACJ,aAAa,EACd,GAAG;YACJ,OAAO,IAAI,MAAM,eAAe;gBAC9B,KAAI,GAAG,EAAE,IAAI;oBACX,IAAI,MAAM,CAAC,KAAK,EAAE;wBAChB,OAAO,MAAM,CAAC,KAAK;oBACrB;oBACA,OAAO,QAAQ,GAAG,CAAC,KAAK;gBAC1B;YACF;QACF;;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/SenderHeader.js"], "sourcesContent": ["import { CloseOutlined } from '@ant-design/icons';\nimport { Button } from 'antd';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nexport const SendHeaderContext = /*#__PURE__*/React.createContext({});\nconst collapseHeight = () => ({\n  height: 0\n});\nconst expandedHeight = ele => ({\n  height: ele.scrollHeight\n});\nexport default function SenderHeader(props) {\n  const {\n    title,\n    onOpenChange,\n    open,\n    children,\n    className,\n    style,\n    classNames: classes = {},\n    styles = {},\n    closable,\n    forceRender\n  } = props;\n  const {\n    prefixCls\n  } = React.useContext(SendHeaderContext);\n  const headerCls = `${prefixCls}-header`;\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    motionEnter: true,\n    motionLeave: true,\n    motionName: `${headerCls}-motion`,\n    leavedClassName: `${headerCls}-motion-hidden`,\n    onEnterStart: collapseHeight,\n    onEnterActive: expandedHeight,\n    onLeaveStart: expandedHeight,\n    onLeaveActive: collapseHeight,\n    visible: open,\n    forceRender: forceRender\n  }, ({\n    className: motionClassName,\n    style: motionStyle\n  }) => {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(headerCls, motionClassName, className),\n      style: {\n        ...motionStyle,\n        ...style\n      }\n    }, (closable !== false || title) && /*#__PURE__*/React.createElement(\"div\", {\n      className:\n      // We follow antd naming standard here.\n      // So the header part is use `-header` suffix.\n      // Though its little bit weird for double `-header`.\n      classNames(`${headerCls}-header`, classes.header),\n      style: {\n        ...styles.header\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: `${headerCls}-title`\n    }, title), closable !== false && /*#__PURE__*/React.createElement(\"div\", {\n      className: `${headerCls}-close`\n    }, /*#__PURE__*/React.createElement(Button, {\n      type: \"text\",\n      icon: /*#__PURE__*/React.createElement(CloseOutlined, null),\n      size: \"small\",\n      onClick: () => {\n        onOpenChange?.(!open);\n      }\n    }))), children && /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(`${headerCls}-content`, classes.content),\n      style: {\n        ...styles.content\n      }\n    }, children));\n  });\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AACA;;;;;;AACO,MAAM,oBAAoB,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,CAAC;AACnE,MAAM,iBAAiB,IAAM,CAAC;QAC5B,QAAQ;IACV,CAAC;AACD,MAAM,iBAAiB,CAAA,MAAO,CAAC;QAC7B,QAAQ,IAAI,YAAY;IAC1B,CAAC;AACc,SAAS,aAAa,KAAK;IACxC,MAAM,EACJ,KAAK,EACL,YAAY,EACZ,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,KAAK,EACL,YAAY,UAAU,CAAC,CAAC,EACxB,SAAS,CAAC,CAAC,EACX,QAAQ,EACR,WAAW,EACZ,GAAG;IACJ,MAAM,EACJ,SAAS,EACV,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrB,MAAM,YAAY,GAAG,UAAU,OAAO,CAAC;IACvC,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,8JAAA,CAAA,UAAS,EAAE;QACjD,aAAa;QACb,aAAa;QACb,YAAY,GAAG,UAAU,OAAO,CAAC;QACjC,iBAAiB,GAAG,UAAU,cAAc,CAAC;QAC7C,cAAc;QACd,eAAe;QACf,cAAc;QACd,eAAe;QACf,SAAS;QACT,aAAa;IACf,GAAG,CAAC,EACF,WAAW,eAAe,EAC1B,OAAO,WAAW,EACnB;QACC,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;YAC7C,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,iBAAiB;YAClD,OAAO;gBACL,GAAG,WAAW;gBACd,GAAG,KAAK;YACV;QACF,GAAG,CAAC,aAAa,SAAS,KAAK,KAAK,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;YAC1E,WACA,uCAAuC;YACvC,8CAA8C;YAC9C,oDAAoD;YACpD,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,UAAU,OAAO,CAAC,EAAE,QAAQ,MAAM;YAChD,OAAO;gBACL,GAAG,OAAO,MAAM;YAClB;QACF,GAAG,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;YACzC,WAAW,GAAG,UAAU,MAAM,CAAC;QACjC,GAAG,QAAQ,aAAa,SAAS,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;YACvE,WAAW,GAAG,UAAU,MAAM,CAAC;QACjC,GAAG,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,SAAM,EAAE;YAC1C,MAAM;YACN,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,uNAAA,CAAA,gBAAa,EAAE;YACtD,MAAM;YACN,SAAS;gBACP,eAAe,CAAC;YAClB;QACF,MAAM,YAAY,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;YACxD,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,UAAU,QAAQ,CAAC,EAAE,QAAQ,OAAO;YAC7D,OAAO;gBACL,GAAG,OAAO,OAAO;YACnB;QACF,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/components/ActionButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { Button } from 'antd';\nimport classNames from 'classnames';\nimport * as React from 'react';\nexport const ActionButtonContext = /*#__PURE__*/React.createContext(null);\nexport function ActionButton(props, ref) {\n  const {\n    className,\n    action,\n    onClick,\n    ...restProps\n  } = props;\n  const context = React.useContext(ActionButtonContext);\n  const {\n    prefixCls,\n    disabled: rootDisabled\n  } = context;\n  const mergedDisabled = restProps.disabled ?? rootDisabled ?? context[`${action}Disabled`];\n  return /*#__PURE__*/React.createElement(Button, _extends({\n    type: \"text\"\n  }, restProps, {\n    ref: ref,\n    onClick: e => {\n      if (mergedDisabled) {\n        return;\n      }\n      context[action]?.();\n      onClick?.(e);\n    },\n    className: classNames(prefixCls, className, {\n      [`${prefixCls}-disabled`]: mergedDisabled\n    })\n  }));\n}\nexport default /*#__PURE__*/React.forwardRef(ActionButton);"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;AACO,MAAM,sBAAsB,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE;AAC7D,SAAS,aAAa,KAAK,EAAE,GAAG;IACrC,MAAM,EACJ,SAAS,EACT,MAAM,EACN,OAAO,EACP,GAAG,WACJ,GAAG;IACJ,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,MAAM,EACJ,SAAS,EACT,UAAU,YAAY,EACvB,GAAG;IACJ,MAAM,iBAAiB,UAAU,QAAQ,IAAI,gBAAgB,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC,CAAC;IACzF,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,SAAM,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACvD,MAAM;IACR,GAAG,WAAW;QACZ,KAAK;QACL,SAAS,CAAA;YACP,IAAI,gBAAgB;gBAClB;YACF;YACA,OAAO,CAAC,OAAO;YACf,UAAU;QACZ;QACA,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,WAAW;YAC1C,CAAC,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE;QAC7B;IACF;AACF;uCACe,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/components/ClearButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ClearOutlined } from '@ant-design/icons';\nimport * as React from 'react';\nimport ActionButton from \"./ActionButton\";\nfunction ClearButton(props, ref) {\n  return /*#__PURE__*/React.createElement(ActionButton, _extends({\n    icon: /*#__PURE__*/React.createElement(ClearOutlined, null)\n  }, props, {\n    action: \"onClear\",\n    ref: ref\n  }));\n}\nexport default /*#__PURE__*/React.forwardRef(ClearButton);"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,YAAY,KAAK,EAAE,GAAG;IAC7B,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,qLAAA,CAAA,UAAY,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC7D,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,uNAAA,CAAA,gBAAa,EAAE;IACxD,GAAG,OAAO;QACR,QAAQ;QACR,KAAK;IACP;AACF;uCACe,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/StopLoading.js"], "sourcesContent": ["import React, { memo } from 'react';\nconst StopLoadingIcon = /*#__PURE__*/memo(props => {\n  const {\n    className\n  } = props;\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    color: \"currentColor\",\n    viewBox: \"0 0 1000 1000\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n    //xmlnsXlink=\"http://www.w3.org/1999/xlink\"\n    ,\n    className: className\n  }, /*#__PURE__*/React.createElement(\"title\", null, \"Stop Loading\"), /*#__PURE__*/React.createElement(\"rect\", {\n    fill: \"currentColor\",\n    height: \"250\",\n    rx: \"24\",\n    ry: \"24\",\n    width: \"250\",\n    x: \"375\",\n    y: \"375\"\n  }), /*#__PURE__*/React.createElement(\"circle\", {\n    cx: \"500\",\n    cy: \"500\",\n    fill: \"none\",\n    r: \"450\",\n    stroke: \"currentColor\",\n    strokeWidth: \"100\",\n    opacity: \"0.45\"\n  }), /*#__PURE__*/React.createElement(\"circle\", {\n    cx: \"500\",\n    cy: \"500\",\n    fill: \"none\",\n    r: \"450\",\n    stroke: \"currentColor\",\n    strokeWidth: \"100\",\n    strokeDasharray: \"600 9999999\"\n  }, /*#__PURE__*/React.createElement(\"animateTransform\", {\n    attributeName: \"transform\",\n    dur: \"1s\",\n    from: \"0 500 500\",\n    repeatCount: \"indefinite\",\n    to: \"***********\",\n    type: \"rotate\"\n  })));\n});\nexport default StopLoadingIcon;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,kBAAkB,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,CAAA;IACxC,MAAM,EACJ,SAAS,EACV,GAAG;IACJ,OAAO,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC7C,OAAO;QACP,SAAS;QACT,OAAO;QAGP,WAAW;IACb,GAAG,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,MAAM,iBAAiB,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;QAC3G,MAAM;QACN,QAAQ;QACR,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,GAAG;QACH,GAAG;IACL,IAAI,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAC7C,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,GAAG;QACH,QAAQ;QACR,aAAa;QACb,SAAS;IACX,IAAI,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAC7C,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,GAAG;QACH,QAAQ;QACR,aAAa;QACb,iBAAiB;IACnB,GAAG,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,oBAAoB;QACtD,eAAe;QACf,KAAK;QACL,MAAM;QACN,aAAa;QACb,IAAI;QACJ,MAAM;IACR;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/components/LoadingButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport StopLoadingIcon from \"../StopLoading\";\nimport ActionButton, { ActionButtonContext } from \"./ActionButton\";\nfunction LoadingButton(props, ref) {\n  const {\n    prefixCls\n  } = React.useContext(ActionButtonContext);\n  const {\n    className\n  } = props;\n  return /*#__PURE__*/React.createElement(ActionButton, _extends({\n    icon: null,\n    color: \"primary\",\n    variant: \"text\",\n    shape: \"circle\"\n  }, props, {\n    className: classNames(className, `${prefixCls}-loading-button`),\n    action: \"onCancel\",\n    ref: ref\n  }), /*#__PURE__*/React.createElement(StopLoadingIcon, {\n    className: `${prefixCls}-loading-icon`\n  }));\n}\nexport default /*#__PURE__*/React.forwardRef(LoadingButton);"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,SAAS,cAAc,KAAK,EAAE,GAAG;IAC/B,MAAM,EACJ,SAAS,EACV,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,qLAAA,CAAA,sBAAmB;IACxC,MAAM,EACJ,SAAS,EACV,GAAG;IACJ,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,qLAAA,CAAA,UAAY,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC7D,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;IACT,GAAG,OAAO;QACR,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,GAAG,UAAU,eAAe,CAAC;QAC9D,QAAQ;QACR,KAAK;IACP,IAAI,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,sKAAA,CAAA,UAAe,EAAE;QACpD,WAAW,GAAG,UAAU,aAAa,CAAC;IACxC;AACF;uCACe,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/components/SendButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ArrowUpOutlined } from '@ant-design/icons';\nimport * as React from 'react';\nimport ActionButton from \"./ActionButton\";\nfunction SendButton(props, ref) {\n  return /*#__PURE__*/React.createElement(ActionButton, _extends({\n    icon: /*#__PURE__*/React.createElement(ArrowUpOutlined, null),\n    type: \"primary\",\n    shape: \"circle\"\n  }, props, {\n    action: \"onSend\",\n    ref: ref\n  }));\n}\nexport default /*#__PURE__*/React.forwardRef(SendButton);"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,WAAW,KAAK,EAAE,GAAG;IAC5B,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,qLAAA,CAAA,UAAY,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC7D,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,2NAAA,CAAA,kBAAe,EAAE;QACxD,MAAM;QACN,OAAO;IACT,GAAG,OAAO;QACR,QAAQ;QACR,KAAK;IACP;AACF;uCACe,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/components/SpeechButton/RecordingIcon.js"], "sourcesContent": ["import React from 'react';\nconst SIZE = 1000;\nconst COUNT = 4;\nconst RECT_WIDTH = 140;\nconst RECT_RADIUS = RECT_WIDTH / 2;\nconst RECT_HEIGHT_MIN = 250;\nconst RECT_HEIGHT_MAX = 500;\nconst DURATION = 0.8;\nexport default function RecordingIcon({\n  className\n}) {\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    color: \"currentColor\",\n    viewBox: `0 0 ${SIZE} ${SIZE}`,\n    xmlns: \"http://www.w3.org/2000/svg\"\n    // xmlnsXlink=\"http://www.w3.org/1999/xlink\"\n    ,\n    className: className\n  }, /*#__PURE__*/React.createElement(\"title\", null, \"Speech Recording\"), Array.from({\n    length: COUNT\n  }).map((_, index) => {\n    const dest = (SIZE - RECT_WIDTH * COUNT) / (COUNT - 1);\n    const x = index * (dest + RECT_WIDTH);\n    const yMin = SIZE / 2 - RECT_HEIGHT_MIN / 2;\n    const yMax = SIZE / 2 - RECT_HEIGHT_MAX / 2;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      fill: \"currentColor\",\n      rx: RECT_RADIUS,\n      ry: RECT_RADIUS,\n      height: RECT_HEIGHT_MIN,\n      width: RECT_WIDTH,\n      x: x,\n      y: yMin,\n      key: index\n    }, /*#__PURE__*/React.createElement(\"animate\", {\n      attributeName: \"height\",\n      values: `${RECT_HEIGHT_MIN}; ${RECT_HEIGHT_MAX}; ${RECT_HEIGHT_MIN}`,\n      keyTimes: \"0; 0.5; 1\",\n      dur: `${DURATION}s`,\n      begin: `${DURATION / COUNT * index}s`,\n      repeatCount: \"indefinite\"\n    }), /*#__PURE__*/React.createElement(\"animate\", {\n      attributeName: \"y\",\n      values: `${yMin}; ${yMax}; ${yMin}`,\n      keyTimes: \"0; 0.5; 1\",\n      dur: `${DURATION}s`,\n      begin: `${DURATION / COUNT * index}s`,\n      repeatCount: \"indefinite\"\n    }));\n  }));\n}"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,aAAa;AACnB,MAAM,cAAc,aAAa;AACjC,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,MAAM,WAAW;AACF,SAAS,cAAc,EACpC,SAAS,EACV;IACC,OAAO,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC7C,OAAO;QACP,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM;QAC9B,OAAO;QAGP,WAAW;IACb,GAAG,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,MAAM,qBAAqB,MAAM,IAAI,CAAC;QACjF,QAAQ;IACV,GAAG,GAAG,CAAC,CAAC,GAAG;QACT,MAAM,OAAO,CAAC,OAAO,aAAa,KAAK,IAAI,CAAC,QAAQ,CAAC;QACrD,MAAM,IAAI,QAAQ,CAAC,OAAO,UAAU;QACpC,MAAM,OAAO,OAAO,IAAI,kBAAkB;QAC1C,MAAM,OAAO,OAAO,IAAI,kBAAkB;QAC1C,OAAO,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,QAAQ;YACR,OAAO;YACP,GAAG;YACH,GAAG;YACH,KAAK;QACP,GAAG,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW;YAC7C,eAAe;YACf,QAAQ,GAAG,gBAAgB,EAAE,EAAE,gBAAgB,EAAE,EAAE,iBAAiB;YACpE,UAAU;YACV,KAAK,GAAG,SAAS,CAAC,CAAC;YACnB,OAAO,GAAG,WAAW,QAAQ,MAAM,CAAC,CAAC;YACrC,aAAa;QACf,IAAI,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW;YAC9C,eAAe;YACf,QAAQ,GAAG,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM;YACnC,UAAU;YACV,KAAK,GAAG,SAAS,CAAC,CAAC;YACnB,OAAO,GAAG,WAAW,QAAQ,MAAM,CAAC,CAAC;YACrC,aAAa;QACf;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/components/SpeechButton/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { AudioMutedOutlined, AudioOutlined } from '@ant-design/icons';\nimport * as React from 'react';\nimport ActionButton, { ActionButtonContext } from \"../ActionButton\";\nimport RecordingIcon from \"./RecordingIcon\";\nfunction SpeechButton(props, ref) {\n  const {\n    speechRecording,\n    onSpeechDisabled,\n    prefixCls\n  } = React.useContext(ActionButtonContext);\n  let icon = null;\n  if (speechRecording) {\n    icon = /*#__PURE__*/React.createElement(RecordingIcon, {\n      className: `${prefixCls}-recording-icon`\n    });\n  } else if (onSpeechDisabled) {\n    icon = /*#__PURE__*/React.createElement(AudioMutedOutlined, null);\n  } else {\n    icon = /*#__PURE__*/React.createElement(AudioOutlined, null);\n  }\n  return /*#__PURE__*/React.createElement(ActionButton, _extends({\n    icon: icon,\n    color: \"primary\",\n    variant: \"text\"\n  }, props, {\n    action: \"onSpeech\",\n    ref: ref\n  }));\n}\nexport default /*#__PURE__*/React.forwardRef(SpeechButton);"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AACA,SAAS,aAAa,KAAK,EAAE,GAAG;IAC9B,MAAM,EACJ,eAAe,EACf,gBAAgB,EAChB,SAAS,EACV,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,qLAAA,CAAA,sBAAmB;IACxC,IAAI,OAAO;IACX,IAAI,iBAAiB;QACnB,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,sMAAA,CAAA,UAAa,EAAE;YACrD,WAAW,GAAG,UAAU,eAAe,CAAC;QAC1C;IACF,OAAO,IAAI,kBAAkB;QAC3B,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,iOAAA,CAAA,qBAAkB,EAAE;IAC9D,OAAO;QACL,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,uNAAA,CAAA,gBAAa,EAAE;IACzD;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,qLAAA,CAAA,UAAY,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC7D,MAAM;QACN,OAAO;QACP,SAAS;IACX,GAAG,OAAO;QACR,QAAQ;QACR,KAAK;IACP;AACF;uCACe,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/style/header.js"], "sourcesContent": ["const genSenderHeaderStyle = token => {\n  const {\n    componentCls,\n    calc\n  } = token;\n  const headerCls = `${componentCls}-header`;\n  return {\n    [componentCls]: {\n      [headerCls]: {\n        borderBottomWidth: token.lineWidth,\n        borderBottomStyle: 'solid',\n        borderBottomColor: token.colorBorder,\n        // ======================== Header ========================\n        '&-header': {\n          background: token.colorFillAlter,\n          fontSize: token.fontSize,\n          lineHeight: token.lineHeight,\n          paddingBlock: calc(token.paddingSM).sub(token.lineWidthBold).equal(),\n          paddingInlineStart: token.padding,\n          paddingInlineEnd: token.paddingXS,\n          display: 'flex',\n          borderRadius: {\n            _skip_check_: true,\n            value: calc(token.borderRadius).mul(2).equal()\n          },\n          borderEndStartRadius: 0,\n          borderEndEndRadius: 0,\n          [`${headerCls}-title`]: {\n            flex: 'auto'\n          }\n        },\n        // ======================= Content ========================\n        '&-content': {\n          padding: token.padding\n        },\n        // ======================== Motion ========================\n        '&-motion': {\n          transition: ['height', 'border'].map(prop => `${prop} ${token.motionDurationSlow}`).join(','),\n          overflow: 'hidden',\n          '&-enter-start, &-leave-active': {\n            borderBottomColor: 'transparent'\n          },\n          '&-hidden': {\n            display: 'none'\n          }\n        }\n      }\n    }\n  };\n};\nexport default genSenderHeaderStyle;"], "names": [], "mappings": ";;;AAAA,MAAM,uBAAuB,CAAA;IAC3B,MAAM,EACJ,YAAY,EACZ,IAAI,EACL,GAAG;IACJ,MAAM,YAAY,GAAG,aAAa,OAAO,CAAC;IAC1C,OAAO;QACL,CAAC,aAAa,EAAE;YACd,CAAC,UAAU,EAAE;gBACX,mBAAmB,MAAM,SAAS;gBAClC,mBAAmB;gBACnB,mBAAmB,MAAM,WAAW;gBACpC,2DAA2D;gBAC3D,YAAY;oBACV,YAAY,MAAM,cAAc;oBAChC,UAAU,MAAM,QAAQ;oBACxB,YAAY,MAAM,UAAU;oBAC5B,cAAc,KAAK,MAAM,SAAS,EAAE,GAAG,CAAC,MAAM,aAAa,EAAE,KAAK;oBAClE,oBAAoB,MAAM,OAAO;oBACjC,kBAAkB,MAAM,SAAS;oBACjC,SAAS;oBACT,cAAc;wBACZ,cAAc;wBACd,OAAO,KAAK,MAAM,YAAY,EAAE,GAAG,CAAC,GAAG,KAAK;oBAC9C;oBACA,sBAAsB;oBACtB,oBAAoB;oBACpB,CAAC,GAAG,UAAU,MAAM,CAAC,CAAC,EAAE;wBACtB,MAAM;oBACR;gBACF;gBACA,2DAA2D;gBAC3D,aAAa;oBACX,SAAS,MAAM,OAAO;gBACxB;gBACA,2DAA2D;gBAC3D,YAAY;oBACV,YAAY;wBAAC;wBAAU;qBAAS,CAAC,GAAG,CAAC,CAAA,OAAQ,GAAG,KAAK,CAAC,EAAE,MAAM,kBAAkB,EAAE,EAAE,IAAI,CAAC;oBACzF,UAAU;oBACV,iCAAiC;wBAC/B,mBAAmB;oBACrB;oBACA,YAAY;wBACV,SAAS;oBACX;gBACF;YACF;QACF;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { mergeToken } from '@ant-design/cssinjs-utils';\nimport { genStyleHooks } from \"../../theme/genStyleUtils\";\nimport genSenderHeaderStyle from \"./header\";\n\n// biome-ignore lint/suspicious/noEmptyInterface: ComponentToken need to be empty by default\n\nconst genSenderStyle = token => {\n  const {\n    componentCls,\n    padding,\n    paddingSM,\n    paddingXS,\n    paddingXXS,\n    lineWidth,\n    lineWidthBold,\n    calc\n  } = token;\n  return {\n    [componentCls]: {\n      position: 'relative',\n      width: '100%',\n      boxSizing: 'border-box',\n      boxShadow: `${token.boxShadowTertiary}`,\n      transition: `background ${token.motionDurationSlow}`,\n      // Border\n      borderRadius: {\n        _skip_check_: true,\n        value: calc(token.borderRadius).mul(2).equal()\n      },\n      borderColor: token.colorBorder,\n      borderWidth: 0,\n      borderStyle: 'solid',\n      // Border\n      '&:after': {\n        content: '\"\"',\n        position: 'absolute',\n        inset: 0,\n        pointerEvents: 'none',\n        transition: `border-color ${token.motionDurationSlow}`,\n        borderRadius: {\n          _skip_check_: true,\n          value: 'inherit'\n        },\n        borderStyle: 'inherit',\n        borderColor: 'inherit',\n        borderWidth: lineWidth\n      },\n      // Focus\n      '&:focus-within': {\n        boxShadow: `${token.boxShadowSecondary}`,\n        borderColor: token.colorPrimary,\n        '&:after': {\n          borderWidth: lineWidthBold\n        }\n      },\n      '&-disabled': {\n        background: token.colorBgContainerDisabled\n      },\n      // ============================== RTL ==============================\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      // ============================ Content ============================\n      [`${componentCls}-content`]: {\n        display: 'flex',\n        gap: paddingXS,\n        width: '100%',\n        paddingBlock: paddingSM,\n        paddingInlineStart: padding,\n        paddingInlineEnd: paddingSM,\n        boxSizing: 'border-box',\n        alignItems: 'flex-end'\n      },\n      // ============================ Prefix =============================\n      [`${componentCls}-prefix`]: {\n        flex: 'none'\n      },\n      // ============================= Input =============================\n      [`${componentCls}-input`]: {\n        padding: 0,\n        borderRadius: 0,\n        flex: 'auto',\n        alignSelf: 'center',\n        minHeight: 'auto'\n      },\n      // ============================ Actions ============================\n      [`${componentCls}-actions-list`]: {\n        flex: 'none',\n        display: 'flex',\n        '&-presets': {\n          gap: token.paddingXS\n        }\n      },\n      [`${componentCls}-actions-btn`]: {\n        '&-disabled': {\n          opacity: 0.45\n        },\n        '&-loading-button': {\n          padding: 0,\n          border: 0\n        },\n        '&-loading-icon': {\n          height: token.controlHeight,\n          width: token.controlHeight,\n          verticalAlign: 'top'\n        },\n        '&-recording-icon': {\n          height: '1.2em',\n          width: '1.2em',\n          verticalAlign: 'top'\n        }\n      },\n      // ============================ Footer =============================\n      [`${componentCls}-footer`]: {\n        paddingInlineStart: padding,\n        paddingInlineEnd: paddingSM,\n        paddingBlockEnd: paddingSM,\n        paddingBlockStart: paddingXXS,\n        boxSizing: 'border-box'\n      }\n    }\n  };\n};\nexport const prepareComponentToken = () => ({});\nexport default genStyleHooks('Sender', token => {\n  const {\n    paddingXS,\n    calc\n  } = token;\n  const SenderToken = mergeToken(token, {\n    SenderContentMaxWidth: `calc(100% - ${unit(calc(paddingXS).add(32).equal())})`\n  });\n  return [genSenderStyle(SenderToken), genSenderHeaderStyle(SenderToken)];\n}, prepareComponentToken);"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,4FAA4F;AAE5F,MAAM,iBAAiB,CAAA;IACrB,MAAM,EACJ,YAAY,EACZ,OAAO,EACP,SAAS,EACT,SAAS,EACT,UAAU,EACV,SAAS,EACT,aAAa,EACb,IAAI,EACL,GAAG;IACJ,OAAO;QACL,CAAC,aAAa,EAAE;YACd,UAAU;YACV,OAAO;YACP,WAAW;YACX,WAAW,GAAG,MAAM,iBAAiB,EAAE;YACvC,YAAY,CAAC,WAAW,EAAE,MAAM,kBAAkB,EAAE;YACpD,SAAS;YACT,cAAc;gBACZ,cAAc;gBACd,OAAO,KAAK,MAAM,YAAY,EAAE,GAAG,CAAC,GAAG,KAAK;YAC9C;YACA,aAAa,MAAM,WAAW;YAC9B,aAAa;YACb,aAAa;YACb,SAAS;YACT,WAAW;gBACT,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,YAAY,CAAC,aAAa,EAAE,MAAM,kBAAkB,EAAE;gBACtD,cAAc;oBACZ,cAAc;oBACd,OAAO;gBACT;gBACA,aAAa;gBACb,aAAa;gBACb,aAAa;YACf;YACA,QAAQ;YACR,kBAAkB;gBAChB,WAAW,GAAG,MAAM,kBAAkB,EAAE;gBACxC,aAAa,MAAM,YAAY;gBAC/B,WAAW;oBACT,aAAa;gBACf;YACF;YACA,cAAc;gBACZ,YAAY,MAAM,wBAAwB;YAC5C;YACA,oEAAoE;YACpE,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC,CAAC,EAAE;gBACxB,WAAW;YACb;YACA,oEAAoE;YACpE,CAAC,GAAG,aAAa,QAAQ,CAAC,CAAC,EAAE;gBAC3B,SAAS;gBACT,KAAK;gBACL,OAAO;gBACP,cAAc;gBACd,oBAAoB;gBACpB,kBAAkB;gBAClB,WAAW;gBACX,YAAY;YACd;YACA,oEAAoE;YACpE,CAAC,GAAG,aAAa,OAAO,CAAC,CAAC,EAAE;gBAC1B,MAAM;YACR;YACA,oEAAoE;YACpE,CAAC,GAAG,aAAa,MAAM,CAAC,CAAC,EAAE;gBACzB,SAAS;gBACT,cAAc;gBACd,MAAM;gBACN,WAAW;gBACX,WAAW;YACb;YACA,oEAAoE;YACpE,CAAC,GAAG,aAAa,aAAa,CAAC,CAAC,EAAE;gBAChC,MAAM;gBACN,SAAS;gBACT,aAAa;oBACX,KAAK,MAAM,SAAS;gBACtB;YACF;YACA,CAAC,GAAG,aAAa,YAAY,CAAC,CAAC,EAAE;gBAC/B,cAAc;oBACZ,SAAS;gBACX;gBACA,oBAAoB;oBAClB,SAAS;oBACT,QAAQ;gBACV;gBACA,kBAAkB;oBAChB,QAAQ,MAAM,aAAa;oBAC3B,OAAO,MAAM,aAAa;oBAC1B,eAAe;gBACjB;gBACA,oBAAoB;oBAClB,QAAQ;oBACR,OAAO;oBACP,eAAe;gBACjB;YACF;YACA,oEAAoE;YACpE,CAAC,GAAG,aAAa,OAAO,CAAC,CAAC,EAAE;gBAC1B,oBAAoB;gBACpB,kBAAkB;gBAClB,iBAAiB;gBACjB,mBAAmB;gBACnB,WAAW;YACb;QACF;IACF;AACF;AACO,MAAM,wBAAwB,IAAM,CAAC,CAAC,CAAC;uCAC/B,CAAA,GAAA,uKAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,CAAA;IACrC,MAAM,EACJ,SAAS,EACT,IAAI,EACL,GAAG;IACJ,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,OAAO;QACpC,uBAAuB,CAAC,YAAY,EAAE,CAAA,GAAA,oKAAA,CAAA,OAAI,AAAD,EAAE,KAAK,WAAW,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IAChF;IACA,OAAO;QAAC,eAAe;QAAc,CAAA,GAAA,0KAAA,CAAA,UAAoB,AAAD,EAAE;KAAa;AACzE,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/useSpeech.js"], "sourcesContent": ["import { useEvent, useMergedState } from 'rc-util';\nimport React from 'react';\n\n// Ensure that the SpeechRecognition API is available in the browser\nlet SpeechRecognition;\nif (!SpeechRecognition && typeof window !== 'undefined') {\n  SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n}\nexport default function useSpeech(onSpeech, allowSpeech) {\n  const onEventSpeech = useEvent(onSpeech);\n\n  // ========================== Speech Config ==========================\n  const [controlledRecording, onControlledRecordingChange, speechInControlled] = React.useMemo(() => {\n    if (typeof allowSpeech === 'object') {\n      return [allowSpeech.recording, allowSpeech.onRecordingChange, typeof allowSpeech.recording === 'boolean'];\n    }\n    return [undefined, undefined, false];\n  }, [allowSpeech]);\n\n  // ======================== Speech Permission ========================\n  const [permissionState, setPermissionState] = React.useState(null);\n  React.useEffect(() => {\n    if (typeof navigator !== 'undefined' && 'permissions' in navigator) {\n      let lastPermission = null;\n      navigator.permissions.query({\n        name: 'microphone'\n      }).then(permissionStatus => {\n        setPermissionState(permissionStatus.state);\n\n        // Keep the last permission status.\n        permissionStatus.onchange = function () {\n          setPermissionState(this.state);\n        };\n        lastPermission = permissionStatus;\n      });\n      return () => {\n        // Avoid memory leaks\n        if (lastPermission) {\n          lastPermission.onchange = null;\n        }\n      };\n    }\n  }, []);\n\n  // Convert permission state to a simple type\n  const mergedAllowSpeech = SpeechRecognition && permissionState !== 'denied';\n\n  // ========================== Speech Events ==========================\n  const recognitionRef = React.useRef(null);\n  const [recording, setRecording] = useMergedState(false, {\n    value: controlledRecording\n  });\n  const forceBreakRef = React.useRef(false);\n  const ensureRecognition = () => {\n    if (mergedAllowSpeech && !recognitionRef.current) {\n      const recognition = new SpeechRecognition();\n      recognition.onstart = () => {\n        setRecording(true);\n      };\n      recognition.onend = () => {\n        setRecording(false);\n      };\n      recognition.onresult = event => {\n        if (!forceBreakRef.current) {\n          const transcript = event.results?.[0]?.[0]?.transcript;\n          onEventSpeech(transcript);\n        }\n        forceBreakRef.current = false;\n      };\n      recognitionRef.current = recognition;\n    }\n  };\n  const triggerSpeech = useEvent(forceBreak => {\n    // Ignore if `forceBreak` but is not recording\n    if (forceBreak && !recording) {\n      return;\n    }\n    forceBreakRef.current = forceBreak;\n    if (speechInControlled) {\n      // If in controlled mode, do nothing\n      onControlledRecordingChange?.(!recording);\n    } else {\n      ensureRecognition();\n      if (recognitionRef.current) {\n        if (recording) {\n          recognitionRef.current.stop();\n          onControlledRecordingChange?.(false);\n        } else {\n          recognitionRef.current.start();\n          onControlledRecordingChange?.(true);\n        }\n      }\n    }\n  });\n  return [mergedAllowSpeech, triggerSpeech, recording];\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;;;AAEA,oEAAoE;AACpE,IAAI;AACJ,IAAI,CAAC,qBAAqB,OAAO,WAAW,aAAa;IACvD,oBAAoB,OAAO,iBAAiB,IAAI,OAAO,uBAAuB;AAChF;AACe,SAAS,UAAU,QAAQ,EAAE,WAAW;IACrD,MAAM,gBAAgB,CAAA,GAAA,+LAAA,CAAA,WAAQ,AAAD,EAAE;IAE/B,sEAAsE;IACtE,MAAM,CAAC,qBAAqB,6BAA6B,mBAAmB,GAAG,6JAAA,CAAA,UAAK,CAAC,OAAO;6BAAC;YAC3F,IAAI,OAAO,gBAAgB,UAAU;gBACnC,OAAO;oBAAC,YAAY,SAAS;oBAAE,YAAY,iBAAiB;oBAAE,OAAO,YAAY,SAAS,KAAK;iBAAU;YAC3G;YACA,OAAO;gBAAC;gBAAW;gBAAW;aAAM;QACtC;4BAAG;QAAC;KAAY;IAEhB,sEAAsE;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7D,6JAAA,CAAA,UAAK,CAAC,SAAS;+BAAC;YACd,IAAI,OAAO,cAAc,eAAe,iBAAiB,WAAW;gBAClE,IAAI,iBAAiB;gBACrB,UAAU,WAAW,CAAC,KAAK,CAAC;oBAC1B,MAAM;gBACR,GAAG,IAAI;2CAAC,CAAA;wBACN,mBAAmB,iBAAiB,KAAK;wBAEzC,mCAAmC;wBACnC,iBAAiB,QAAQ;mDAAG;gCAC1B,mBAAmB,IAAI,CAAC,KAAK;4BAC/B;;wBACA,iBAAiB;oBACnB;;gBACA;2CAAO;wBACL,qBAAqB;wBACrB,IAAI,gBAAgB;4BAClB,eAAe,QAAQ,GAAG;wBAC5B;oBACF;;YACF;QACF;8BAAG,EAAE;IAEL,4CAA4C;IAC5C,MAAM,oBAAoB,qBAAqB,oBAAoB;IAEnE,sEAAsE;IACtE,MAAM,iBAAiB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACpC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,2MAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;QACtD,OAAO;IACT;IACA,MAAM,gBAAgB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACnC,MAAM,oBAAoB;QACxB,IAAI,qBAAqB,CAAC,eAAe,OAAO,EAAE;YAChD,MAAM,cAAc,IAAI;YACxB,YAAY,OAAO,GAAG;gBACpB,aAAa;YACf;YACA,YAAY,KAAK,GAAG;gBAClB,aAAa;YACf;YACA,YAAY,QAAQ,GAAG,CAAA;gBACrB,IAAI,CAAC,cAAc,OAAO,EAAE;oBAC1B,MAAM,aAAa,MAAM,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;oBAC5C,cAAc;gBAChB;gBACA,cAAc,OAAO,GAAG;YAC1B;YACA,eAAe,OAAO,GAAG;QAC3B;IACF;IACA,MAAM,gBAAgB,CAAA,GAAA,+LAAA,CAAA,WAAQ,AAAD;6CAAE,CAAA;YAC7B,8CAA8C;YAC9C,IAAI,cAAc,CAAC,WAAW;gBAC5B;YACF;YACA,cAAc,OAAO,GAAG;YACxB,IAAI,oBAAoB;gBACtB,oCAAoC;gBACpC,8BAA8B,CAAC;YACjC,OAAO;gBACL;gBACA,IAAI,eAAe,OAAO,EAAE;oBAC1B,IAAI,WAAW;wBACb,eAAe,OAAO,CAAC,IAAI;wBAC3B,8BAA8B;oBAChC,OAAO;wBACL,eAAe,OAAO,CAAC,KAAK;wBAC5B,8BAA8B;oBAChC;gBACF;YACF;QACF;;IACA,OAAO;QAAC;QAAmB;QAAe;KAAU;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AntDesignX/agentx/agentx-app/node_modules/%40ant-design/x/es/sender/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { Flex, Input } from 'antd';\nimport classnames from 'classnames';\nimport { useMergedState } from 'rc-util';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport getValue from \"rc-util/es/utils/get\";\nimport React from 'react';\nimport useProxyImperativeHandle from \"../_util/hooks/use-proxy-imperative-handle\";\nimport useXComponentConfig from \"../_util/hooks/use-x-component-config\";\nimport { useXProviderContext } from \"../x-provider\";\nimport SenderHeader, { SendHeaderContext } from \"./SenderHeader\";\nimport { ActionButtonContext } from \"./components/ActionButton\";\nimport ClearButton from \"./components/ClearButton\";\nimport LoadingButton from \"./components/LoadingButton\";\nimport SendButton from \"./components/SendButton\";\nimport SpeechButton from \"./components/SpeechButton\";\nimport useStyle from \"./style\";\nimport useSpeech from \"./useSpeech\";\nfunction getComponent(components, path, defaultComponent) {\n  return getValue(components, path) || defaultComponent;\n}\n\n/** Used for actions render needed components */\nconst sharedRenderComponents = {\n  SendButton,\n  ClearButton,\n  LoadingButton,\n  SpeechButton\n};\nconst ForwardSender = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    prefixCls: customizePrefixCls,\n    styles = {},\n    classNames = {},\n    className,\n    rootClassName,\n    style,\n    defaultValue,\n    value,\n    readOnly,\n    submitType = 'enter',\n    onSubmit,\n    loading,\n    components,\n    onCancel,\n    onChange,\n    actions,\n    onKeyPress,\n    onKeyDown,\n    disabled,\n    allowSpeech,\n    prefix,\n    footer,\n    header,\n    onPaste,\n    onPasteFile,\n    autoSize = {\n      maxRows: 8\n    },\n    ...rest\n  } = props;\n\n  // ============================= MISC =============================\n  const {\n    direction,\n    getPrefixCls\n  } = useXProviderContext();\n  const prefixCls = getPrefixCls('sender', customizePrefixCls);\n\n  // ============================= Refs =============================\n  const containerRef = React.useRef(null);\n  const inputRef = React.useRef(null);\n  useProxyImperativeHandle(ref, () => ({\n    nativeElement: containerRef.current,\n    focus: inputRef.current?.focus,\n    blur: inputRef.current?.blur\n  }));\n\n  // ======================= Component Config =======================\n  const contextConfig = useXComponentConfig('sender');\n  const inputCls = `${prefixCls}-input`;\n\n  // ============================ Styles ============================\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const mergedCls = classnames(prefixCls, contextConfig.className, className, rootClassName, hashId, cssVarCls, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-disabled`]: disabled\n  });\n  const actionBtnCls = `${prefixCls}-actions-btn`;\n  const actionListCls = `${prefixCls}-actions-list`;\n\n  // ============================ Value =============================\n  const [innerValue, setInnerValue] = useMergedState(defaultValue || '', {\n    value\n  });\n  const triggerValueChange = (nextValue, event) => {\n    setInnerValue(nextValue);\n    if (onChange) {\n      onChange(nextValue, event);\n    }\n  };\n\n  // ============================ Speech ============================\n  const [speechPermission, triggerSpeech, speechRecording] = useSpeech(transcript => {\n    triggerValueChange(`${innerValue} ${transcript}`);\n  }, allowSpeech);\n\n  // ========================== Components ==========================\n  const InputTextArea = getComponent(components, ['input'], Input.TextArea);\n  const domProps = pickAttrs(rest, {\n    attr: true,\n    aria: true,\n    data: true\n  });\n  const inputProps = {\n    ...domProps,\n    ref: inputRef\n  };\n\n  // ============================ Events ============================\n  const triggerSend = () => {\n    if (innerValue && onSubmit && !loading) {\n      onSubmit(innerValue);\n    }\n  };\n  const triggerClear = () => {\n    triggerValueChange('');\n  };\n\n  // ============================ Submit ============================\n  const isCompositionRef = React.useRef(false);\n  const onInternalCompositionStart = () => {\n    isCompositionRef.current = true;\n  };\n  const onInternalCompositionEnd = () => {\n    isCompositionRef.current = false;\n  };\n  const onInternalKeyPress = e => {\n    const canSubmit = e.key === 'Enter' && !isCompositionRef.current;\n\n    // Check for `submitType` to submit\n    switch (submitType) {\n      case 'enter':\n        if (canSubmit && !e.shiftKey) {\n          e.preventDefault();\n          triggerSend();\n        }\n        break;\n      case 'shiftEnter':\n        if (canSubmit && e.shiftKey) {\n          e.preventDefault();\n          triggerSend();\n        }\n        break;\n    }\n    onKeyPress?.(e);\n  };\n\n  // ============================ Paste =============================\n  const onInternalPaste = e => {\n    // Get files\n    const files = e.clipboardData?.files;\n    if (files?.length && onPasteFile) {\n      onPasteFile(files[0], files);\n      e.preventDefault();\n    }\n    onPaste?.(e);\n  };\n\n  // ============================ Focus =============================\n  const onContentMouseDown = e => {\n    // If input focused but click on the container,\n    // input will lose focus.\n    // We call `preventDefault` to prevent this behavior\n    if (e.target !== containerRef.current?.querySelector(`.${inputCls}`)) {\n      e.preventDefault();\n    }\n    inputRef.current?.focus();\n  };\n\n  // ============================ Action ============================\n  let actionNode = /*#__PURE__*/React.createElement(Flex, {\n    className: `${actionListCls}-presets`\n  }, allowSpeech && /*#__PURE__*/React.createElement(SpeechButton, null), loading ? /*#__PURE__*/React.createElement(LoadingButton, null) : /*#__PURE__*/React.createElement(SendButton, null));\n\n  // Custom actions\n  if (typeof actions === 'function') {\n    actionNode = actions(actionNode, {\n      components: sharedRenderComponents\n    });\n  } else if (actions || actions === false) {\n    actionNode = actions;\n  }\n  // Custom actions context props\n  const actionsButtonContextProps = {\n    prefixCls: actionBtnCls,\n    onSend: triggerSend,\n    onSendDisabled: !innerValue,\n    onClear: triggerClear,\n    onClearDisabled: !innerValue,\n    onCancel,\n    onCancelDisabled: !loading,\n    onSpeech: () => triggerSpeech(false),\n    onSpeechDisabled: !speechPermission,\n    speechRecording,\n    disabled\n  };\n\n  // ============================ Footer ============================\n  const footerNode = typeof footer === 'function' ? footer({\n    components: sharedRenderComponents\n  }) : footer || null;\n\n  // ============================ Render ============================\n  return wrapCSSVar( /*#__PURE__*/React.createElement(\"div\", {\n    ref: containerRef,\n    className: mergedCls,\n    style: {\n      ...contextConfig.style,\n      ...style\n    }\n  }, header && /*#__PURE__*/React.createElement(SendHeaderContext.Provider, {\n    value: {\n      prefixCls\n    }\n  }, header), /*#__PURE__*/React.createElement(ActionButtonContext.Provider, {\n    value: actionsButtonContextProps\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-content`,\n    onMouseDown: onContentMouseDown\n  }, prefix && /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(`${prefixCls}-prefix`, contextConfig.classNames.prefix, classNames.prefix),\n    style: {\n      ...contextConfig.styles.prefix,\n      ...styles.prefix\n    }\n  }, prefix), /*#__PURE__*/React.createElement(InputTextArea, _extends({}, inputProps, {\n    disabled: disabled,\n    style: {\n      ...contextConfig.styles.input,\n      ...styles.input\n    },\n    className: classnames(inputCls, contextConfig.classNames.input, classNames.input),\n    autoSize: autoSize,\n    value: innerValue,\n    onChange: event => {\n      triggerValueChange(event.target.value, event);\n      triggerSpeech(true);\n    },\n    onPressEnter: onInternalKeyPress,\n    onCompositionStart: onInternalCompositionStart,\n    onCompositionEnd: onInternalCompositionEnd,\n    onKeyDown: onKeyDown,\n    onPaste: onInternalPaste,\n    variant: \"borderless\",\n    readOnly: readOnly\n  })), actionNode && /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(actionListCls, contextConfig.classNames.actions, classNames.actions),\n    style: {\n      ...contextConfig.styles.actions,\n      ...styles.actions\n    }\n  }, actionNode)), footerNode && /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(`${prefixCls}-footer`, contextConfig.classNames.footer, classNames.footer),\n    style: {\n      ...contextConfig.styles.footer,\n      ...styles.footer\n    }\n  }, footerNode))));\n});\nconst Sender = ForwardSender;\nif (process.env.NODE_ENV !== 'production') {\n  Sender.displayName = 'Sender';\n}\nSender.Header = SenderHeader;\nexport default Sender;"], "names": [], "mappings": ";;;AA+QI;AA/QJ;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AACA,SAAS,aAAa,UAAU,EAAE,IAAI,EAAE,gBAAgB;IACtD,OAAO,CAAA,GAAA,mJAAA,CAAA,UAAQ,AAAD,EAAE,YAAY,SAAS;AACvC;AAEA,8CAA8C,GAC9C,MAAM,yBAAyB;IAC7B,YAAA,mLAAA,CAAA,UAAU;IACV,aAAA,oLAAA,CAAA,UAAW;IACX,eAAA,sLAAA,CAAA,UAAa;IACb,cAAA,8LAAA,CAAA,UAAY;AACd;AACA,MAAM,gBAAgB,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC,CAAC,OAAO;IAC1D,MAAM,EACJ,WAAW,kBAAkB,EAC7B,SAAS,CAAC,CAAC,EACX,aAAa,CAAC,CAAC,EACf,SAAS,EACT,aAAa,EACb,KAAK,EACL,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,aAAa,OAAO,EACpB,QAAQ,EACR,OAAO,EACP,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,UAAU,EACV,SAAS,EACT,QAAQ,EACR,WAAW,EACX,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,WAAW,EACX,WAAW;QACT,SAAS;IACX,CAAC,EACD,GAAG,MACJ,GAAG;IAEJ,mEAAmE;IACnE,MAAM,EACJ,SAAS,EACT,YAAY,EACb,GAAG,CAAA,GAAA,4PAAA,CAAA,sBAAmB,AAAD;IACtB,MAAM,YAAY,aAAa,UAAU;IAEzC,mEAAmE;IACnE,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAClC,MAAM,WAAW,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,CAAA,GAAA,uMAAA,CAAA,UAAwB,AAAD,EAAE;kDAAK,IAAM,CAAC;gBACnC,eAAe,aAAa,OAAO;gBACnC,OAAO,SAAS,OAAO,EAAE;gBACzB,MAAM,SAAS,OAAO,EAAE;YAC1B,CAAC;;IAED,mEAAmE;IACnE,MAAM,gBAAgB,CAAA,GAAA,kMAAA,CAAA,UAAmB,AAAD,EAAE;IAC1C,MAAM,WAAW,GAAG,UAAU,MAAM,CAAC;IAErC,mEAAmE;IACnE,MAAM,CAAC,YAAY,QAAQ,UAAU,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAQ,AAAD,EAAE;IACjD,MAAM,YAAY,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,cAAc,SAAS,EAAE,WAAW,eAAe,QAAQ,WAAW;QAC5G,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC,EAAE,cAAc;QACpC,CAAC,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE;IAC7B;IACA,MAAM,eAAe,GAAG,UAAU,YAAY,CAAC;IAC/C,MAAM,gBAAgB,GAAG,UAAU,aAAa,CAAC;IAEjD,mEAAmE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,2MAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,IAAI;QACrE;IACF;IACA,MAAM,qBAAqB,CAAC,WAAW;QACrC,cAAc;QACd,IAAI,UAAU;YACZ,SAAS,WAAW;QACtB;IACF;IAEA,mEAAmE;IACnE,MAAM,CAAC,kBAAkB,eAAe,gBAAgB,GAAG,CAAA,GAAA,oKAAA,CAAA,UAAS,AAAD;mCAAE,CAAA;YACnE,mBAAmB,GAAG,WAAW,CAAC,EAAE,YAAY;QAClD;kCAAG;IAEH,mEAAmE;IACnE,MAAM,gBAAgB,aAAa,YAAY;QAAC;KAAQ,EAAE,mLAAA,CAAA,QAAK,CAAC,QAAQ;IACxE,MAAM,WAAW,CAAA,GAAA,gJAAA,CAAA,UAAS,AAAD,EAAE,MAAM;QAC/B,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM,aAAa;QACjB,GAAG,QAAQ;QACX,KAAK;IACP;IAEA,mEAAmE;IACnE,MAAM,cAAc;QAClB,IAAI,cAAc,YAAY,CAAC,SAAS;YACtC,SAAS;QACX;IACF;IACA,MAAM,eAAe;QACnB,mBAAmB;IACrB;IAEA,mEAAmE;IACnE,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACtC,MAAM,6BAA6B;QACjC,iBAAiB,OAAO,GAAG;IAC7B;IACA,MAAM,2BAA2B;QAC/B,iBAAiB,OAAO,GAAG;IAC7B;IACA,MAAM,qBAAqB,CAAA;QACzB,MAAM,YAAY,EAAE,GAAG,KAAK,WAAW,CAAC,iBAAiB,OAAO;QAEhE,mCAAmC;QACnC,OAAQ;YACN,KAAK;gBACH,IAAI,aAAa,CAAC,EAAE,QAAQ,EAAE;oBAC5B,EAAE,cAAc;oBAChB;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,aAAa,EAAE,QAAQ,EAAE;oBAC3B,EAAE,cAAc;oBAChB;gBACF;gBACA;QACJ;QACA,aAAa;IACf;IAEA,mEAAmE;IACnE,MAAM,kBAAkB,CAAA;QACtB,YAAY;QACZ,MAAM,QAAQ,EAAE,aAAa,EAAE;QAC/B,IAAI,OAAO,UAAU,aAAa;YAChC,YAAY,KAAK,CAAC,EAAE,EAAE;YACtB,EAAE,cAAc;QAClB;QACA,UAAU;IACZ;IAEA,mEAAmE;IACnE,MAAM,qBAAqB,CAAA;QACzB,+CAA+C;QAC/C,yBAAyB;QACzB,oDAAoD;QACpD,IAAI,EAAE,MAAM,KAAK,aAAa,OAAO,EAAE,cAAc,CAAC,CAAC,EAAE,UAAU,GAAG;YACpE,EAAE,cAAc;QAClB;QACA,SAAS,OAAO,EAAE;IACpB;IAEA,mEAAmE;IACnE,IAAI,aAAa,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iLAAA,CAAA,OAAI,EAAE;QACtD,WAAW,GAAG,cAAc,QAAQ,CAAC;IACvC,GAAG,eAAe,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,8LAAA,CAAA,UAAY,EAAE,OAAO,UAAU,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,sLAAA,CAAA,UAAa,EAAE,QAAQ,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,mLAAA,CAAA,UAAU,EAAE;IAEvL,iBAAiB;IACjB,IAAI,OAAO,YAAY,YAAY;QACjC,aAAa,QAAQ,YAAY;YAC/B,YAAY;QACd;IACF,OAAO,IAAI,WAAW,YAAY,OAAO;QACvC,aAAa;IACf;IACA,+BAA+B;IAC/B,MAAM,4BAA4B;QAChC,WAAW;QACX,QAAQ;QACR,gBAAgB,CAAC;QACjB,SAAS;QACT,iBAAiB,CAAC;QAClB;QACA,kBAAkB,CAAC;QACnB,UAAU,IAAM,cAAc;QAC9B,kBAAkB,CAAC;QACnB;QACA;IACF;IAEA,mEAAmE;IACnE,MAAM,aAAa,OAAO,WAAW,aAAa,OAAO;QACvD,YAAY;IACd,KAAK,UAAU;IAEf,mEAAmE;IACnE,OAAO,WAAY,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACzD,KAAK;QACL,WAAW;QACX,OAAO;YACL,GAAG,cAAc,KAAK;YACtB,GAAG,KAAK;QACV;IACF,GAAG,UAAU,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,uKAAA,CAAA,oBAAiB,CAAC,QAAQ,EAAE;QACxE,OAAO;YACL;QACF;IACF,GAAG,SAAS,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qLAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE;QACzE,OAAO;IACT,GAAG,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACzC,WAAW,GAAG,UAAU,QAAQ,CAAC;QACjC,aAAa;IACf,GAAG,UAAU,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACnD,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,UAAU,OAAO,CAAC,EAAE,cAAc,UAAU,CAAC,MAAM,EAAE,WAAW,MAAM;QAC/F,OAAO;YACL,GAAG,cAAc,MAAM,CAAC,MAAM;YAC9B,GAAG,OAAO,MAAM;QAClB;IACF,GAAG,SAAS,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,YAAY;QACnF,UAAU;QACV,OAAO;YACL,GAAG,cAAc,MAAM,CAAC,KAAK;YAC7B,GAAG,OAAO,KAAK;QACjB;QACA,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,UAAU,cAAc,UAAU,CAAC,KAAK,EAAE,WAAW,KAAK;QAChF,UAAU;QACV,OAAO;QACP,UAAU,CAAA;YACR,mBAAmB,MAAM,MAAM,CAAC,KAAK,EAAE;YACvC,cAAc;QAChB;QACA,cAAc;QACd,oBAAoB;QACpB,kBAAkB;QAClB,WAAW;QACX,SAAS;QACT,SAAS;QACT,UAAU;IACZ,KAAK,cAAc,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACzD,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,eAAe,cAAc,UAAU,CAAC,OAAO,EAAE,WAAW,OAAO;QACzF,OAAO;YACL,GAAG,cAAc,MAAM,CAAC,OAAO;YAC/B,GAAG,OAAO,OAAO;QACnB;IACF,GAAG,cAAc,cAAc,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACrE,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,UAAU,OAAO,CAAC,EAAE,cAAc,UAAU,CAAC,MAAM,EAAE,WAAW,MAAM;QAC/F,OAAO;YACL,GAAG,cAAc,MAAM,CAAC,MAAM;YAC9B,GAAG,OAAO,MAAM;QAClB;IACF,GAAG;AACL;AACA,MAAM,SAAS;AACf,wCAA2C;IACzC,OAAO,WAAW,GAAG;AACvB;AACA,OAAO,MAAM,GAAG,uKAAA,CAAA,UAAY;uCACb", "ignoreList": [0], "debugId": null}}]}