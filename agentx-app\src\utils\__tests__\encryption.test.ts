/**
 * @jest-environment jsdom
 */

import { encryptValue, decryptValue, clearEncryptionKeys, isEncryptionSupported } from '../encryption';

// Mock crypto for testing
const mockCrypto = {
  subtle: {
    importKey: jest.fn(),
    deriveKey: jest.fn(),
    encrypt: jest.fn(),
    decrypt: jest.fn(),
  },
  getRandomValues: jest.fn((arr: Uint8Array) => {
    // Fill with predictable values for testing
    for (let i = 0; i < arr.length; i++) {
      arr[i] = i % 256;
    }
    return arr;
  }),
};

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};

Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true,
});

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

Object.defineProperty(global, 'btoa', {
  value: (str: string) => Buffer.from(str, 'binary').toString('base64'),
  writable: true,
});

Object.defineProperty(global, 'atob', {
  value: (str: string) => Buffer.from(str, 'base64').toString('binary'),
  writable: true,
});

describe('Encryption Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('isEncryptionSupported', () => {
    it('should return true when crypto is available', () => {
      expect(isEncryptionSupported()).toBe(true);
    });

    it('should return false when crypto is not available', () => {
      const originalCrypto = global.crypto;
      // @ts-ignore
      global.crypto = undefined;
      expect(isEncryptionSupported()).toBe(false);
      global.crypto = originalCrypto;
    });
  });

  describe('clearEncryptionKeys', () => {
    it('should remove master key from localStorage', () => {
      clearEncryptionKeys();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('agentx_master_key');
    });
  });

  describe('encryptValue and decryptValue', () => {
    const mockKey = { type: 'secret' };
    const mockEncryptedData = new ArrayBuffer(16);

    beforeEach(() => {
      mockCrypto.subtle.importKey.mockResolvedValue(mockKey);
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedData);
      mockCrypto.subtle.decrypt.mockResolvedValue(new TextEncoder().encode('test value'));
    });

    it('should encrypt and decrypt a value successfully', async () => {
      const testValue = 'test api key';
      
      // Test encryption
      const encrypted = await encryptValue(testValue);
      expect(typeof encrypted).toBe('string');
      expect(encrypted.length).toBeGreaterThan(0);
      
      // Verify crypto methods were called
      expect(mockCrypto.subtle.importKey).toHaveBeenCalled();
      expect(mockCrypto.subtle.deriveKey).toHaveBeenCalled();
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalled();
    });

    it('should handle encryption errors gracefully', async () => {
      mockCrypto.subtle.encrypt.mockRejectedValue(new Error('Encryption failed'));
      
      await expect(encryptValue('test')).rejects.toThrow('Failed to encrypt value');
    });

    it('should handle decryption errors gracefully', async () => {
      mockCrypto.subtle.decrypt.mockRejectedValue(new Error('Decryption failed'));
      
      await expect(decryptValue('invalid')).rejects.toThrow('Failed to decrypt value');
    });
  });
});
