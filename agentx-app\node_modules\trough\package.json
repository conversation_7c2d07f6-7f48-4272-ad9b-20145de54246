{"name": "trough", "version": "2.2.0", "description": "`trough` is middleware", "license": "MIT", "keywords": ["middleware", "ware"], "repository": "wooorm/trough", "bugs": "https://github.com/wooorm/trough/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts.map", "index.d.ts", "index.js"], "devDependencies": {"@types/node": "^20.0.0", "c8": "^9.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.56.0"}, "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . --frail --output --quiet && prettier . --log-level warn --write && xo --fix", "prepack": "npm run build && npm run format", "test": "npm run build && npm run format && npm run test-coverage", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api"}, "prettier": {"bracketSpacing": false, "singleQuote": true, "semi": false, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true, "#": "some nessecary `any`s", "ignoreFiles": ["lib/index.js", "lib/index.d.ts"]}, "xo": {"prettier": true, "rules": {"capitalized-comments": "off"}}}