'use client';

import React, { useState, useRef } from 'react';
import { Bubble, Sender } from '@ant-design/x';
import { Empty, Spin, Alert } from 'antd';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useChat } from '@/hooks/useChat';
import '@/styles/markdown.css';

interface ChatInterfaceProps {
  conversationId?: string;
}

export default function ChatInterface({ conversationId }: ChatInterfaceProps) {
  const { messages, isLoading, error, sendMessage } = useChat({
    conversationId,
  });
  const [inputValue, setInputValue] = useState('');
  const senderRef = useRef<any>(null);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Clear the input immediately
    setInputValue('');

    // Also clear the Sender component's internal state
    if (senderRef.current) {
      senderRef.current.clear?.();
    }

    await sendMessage(content);
  };

  // Convert messages to Bubble.List format
  const bubbleItems = messages.map((message) => ({
    key: message.id,
    content: message.role === 'assistant' ? (
      <div className="markdown-content">
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {message.content}
        </ReactMarkdown>
      </div>
    ) : message.content,
    placement: message.role === 'user' ? ('end' as const) : ('start' as const),
    avatar: message.role === 'user' ? { style: { backgroundColor: '#1677ff' } } : undefined,
    loading: message.role === 'assistant' && isLoading && message === messages[messages.length - 1],
    variant: message.role === 'assistant' ? 'shadow' : undefined,
  }));

  return (
    <div
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        padding: '24px',
        gap: '16px',
      }}
    >
      {/* Chat Messages Area */}
      <div
        style={{
          flex: 1,
          overflow: 'auto',
          padding: '16px',
          border: '1px solid #f0f0f0',
          borderRadius: '8px',
          backgroundColor: '#fafafa',
        }}
      >
        {messages.length === 0 ? (
          <div
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Empty
              description="Start a conversation"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          <Bubble.List
            items={bubbleItems}
            style={{ height: '100%' }}
          />
        )}

        {isLoading && messages.length > 0 && (
          <div style={{ textAlign: 'center', padding: '16px' }}>
            <Spin size="small" />
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          closable
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* Message Input */}
      <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '16px' }}>
        <Sender
          ref={senderRef}
          placeholder="Type your message here..."
          value={inputValue}
          onChange={setInputValue}
          onSubmit={handleSendMessage}
          loading={isLoading}
          style={{
            borderRadius: '8px',
          }}
        />
      </div>
    </div>
  );
}
