'use client';

import React, { useState, useRef } from 'react';
import { B<PERSON><PERSON>, Sender } from '@ant-design/x';
import { Empty, Spin, Alert, Button } from 'antd';
import { EyeOutlined, CodeOutlined, CopyOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useChat } from '@/hooks/useChat';
import '@/styles/markdown.css';

interface ChatInterfaceProps {
  conversationId?: string;
}

// AI消息组件，支持切换原始数据和渲染数据
function AIMessageContent({ content }: { content: string }) {
  const [showRaw, setShowRaw] = useState(false);
  const [copyStatus, setCopyStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // 复制内容到剪贴板
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopyStatus('success');
      setTimeout(() => setCopyStatus('idle'), 2000); // 2秒后重置状态
    } catch (err) {
      console.error('Failed to copy: ', err);
      setCopyStatus('error');
      setTimeout(() => setCopyStatus('idle'), 2000); // 2秒后重置状态
    }
  };

  return (
    <div>
      {/* 消息内容 */}
      {showRaw ? (
        <div
          style={{
            backgroundColor: '#f6f8fa',
            padding: '12px',
            borderRadius: '6px',
            border: '1px solid #e1e4e8',
            fontFamily: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
            fontSize: '13px',
            whiteSpace: 'pre-wrap',
            color: '#24292e',
            lineHeight: '1.45',
            maxHeight: '400px',
            overflow: 'auto',
          }}
        >
          {content}
        </div>
      ) : (
        <div className="markdown-content">
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {content}
          </ReactMarkdown>
        </div>
      )}

      {/* 操作按钮 */}
      <div style={{ marginTop: '8px', textAlign: 'right', display: 'flex', justifyContent: 'flex-end', gap: '4px' }}>
        <Button
          type="text"
          size="small"
          icon={<CopyOutlined />}
          onClick={handleCopy}
          style={{
            fontSize: '12px',
            height: '24px',
            padding: '0 8px',
            color: copyStatus === 'success' ? '#52c41a' : copyStatus === 'error' ? '#ff4d4f' : '#666',
          }}
        >
          {copyStatus === 'success' ? 'Copied!' : copyStatus === 'error' ? 'Failed' : 'Copy'}
        </Button>
        <Button
          type="text"
          size="small"
          icon={showRaw ? <EyeOutlined /> : <CodeOutlined />}
          onClick={() => setShowRaw(!showRaw)}
          style={{
            fontSize: '12px',
            height: '24px',
            padding: '0 8px',
            color: '#666',
          }}
        >
          {showRaw ? 'Render' : 'Raw'}
        </Button>
      </div>
    </div>
  );
}

export default function ChatInterface({ conversationId }: ChatInterfaceProps) {
  const { messages, isLoading, error, sendMessage } = useChat({
    conversationId,
  });
  const [inputValue, setInputValue] = useState('');
  const senderRef = useRef<any>(null);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Clear the input immediately
    setInputValue('');

    // Also clear the Sender component's internal state
    if (senderRef.current) {
      senderRef.current.clear?.();
    }

    await sendMessage(content);
  };

  // Convert messages to Bubble.List format
  const bubbleItems = messages.map((message) => ({
    key: message.id,
    content: message.role === 'assistant' ? (
      <AIMessageContent content={message.content} />
    ) : (
      <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
        {message.content}
      </div>
    ),
    placement: message.role === 'user' ? ('end' as const) : ('start' as const),
    avatar: message.role === 'user' ? { style: { backgroundColor: '#1677ff' } } : undefined,
    loading: message.role === 'assistant' && isLoading && message === messages[messages.length - 1],
    variant: message.role === 'assistant' ? 'shadow' : undefined,
  }));

  return (
    <div
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        padding: '24px',
        gap: '16px',
      }}
    >
      {/* Chat Messages Area */}
      <div
        style={{
          flex: 1,
          overflow: 'auto',
          padding: '16px',
          border: '1px solid #f0f0f0',
          borderRadius: '8px',
          backgroundColor: '#fafafa',
        }}
      >
        {messages.length === 0 ? (
          <div
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Empty
              description="Start a conversation"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          <Bubble.List
            items={bubbleItems}
            style={{ height: '100%' }}
          />
        )}

        {isLoading && messages.length > 0 && (
          <div style={{ textAlign: 'center', padding: '16px' }}>
            <Spin size="small" />
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          closable
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* Message Input */}
      <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '16px' }}>
        <Sender
          ref={senderRef}
          placeholder="Type your message here..."
          value={inputValue}
          onChange={setInputValue}
          onSubmit={handleSendMessage}
          loading={isLoading}
          style={{
            borderRadius: '8px',
          }}
        />
      </div>
    </div>
  );
}
