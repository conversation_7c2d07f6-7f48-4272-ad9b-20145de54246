'use client';

import React, { useState, useRef } from 'react';
import { Bubble, Sender } from '@ant-design/x';
import { Empty, Spin, Alert } from 'antd';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useChat } from '@/hooks/useChat';

interface ChatInterfaceProps {
  conversationId?: string;
}

export default function ChatInterface({ conversationId }: ChatInterfaceProps) {
  const { messages, isLoading, error, sendMessage } = useChat({
    conversationId,
  });
  const [inputValue, setInputValue] = useState('');
  const senderRef = useRef<any>(null);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Clear the input immediately
    setInputValue('');

    // Also clear the Sender component's internal state
    if (senderRef.current) {
      senderRef.current.clear?.();
    }

    await sendMessage(content);
  };

  // Convert messages to Bubble.List format
  const bubbleItems = messages.map((message) => ({
    key: message.id,
    content: message.role === 'assistant' ? (
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // 自定义代码块样式
          code: ({ node, inline, className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || '');
            return !inline ? (
              <div
                style={{
                  backgroundColor: '#f6f8fa',
                  padding: '16px',
                  borderRadius: '6px',
                  overflow: 'auto',
                  fontSize: '14px',
                  lineHeight: '1.45',
                  margin: '16px 0',
                  border: '1px solid #e1e4e8',
                  fontFamily: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
                  whiteSpace: 'pre',
                  color: '#24292e',
                }}
                {...props}
              >
                {children}
              </div>
            ) : (
              <span
                style={{
                  backgroundColor: '#f6f8fa',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '0.9em',
                  fontFamily: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
                  border: '1px solid #e1e4e8',
                  color: '#24292e',
                  whiteSpace: 'nowrap',
                }}
              >
                {children}
              </span>
            );
          },
          // 自定义 pre 标签，避免嵌套问题
          pre: ({ children }) => (
            <div style={{ margin: '16px 0' }}>
              {children}
            </div>
          ),
          // 自定义段落样式
          p: ({ children }) => (
            <p style={{
              margin: '0 0 12px 0',
              lineHeight: '1.6',
              color: '#24292e',
              fontSize: '14px',
            }}>
              {children}
            </p>
          ),
          // 自定义列表样式
          ul: ({ children }) => (
            <ul style={{
              margin: '0 0 12px 0',
              paddingLeft: '20px',
              color: '#24292e',
              fontSize: '14px',
            }}>
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol style={{
              margin: '0 0 12px 0',
              paddingLeft: '20px',
              color: '#24292e',
              fontSize: '14px',
            }}>
              {children}
            </ol>
          ),
          // 自定义标题样式
          h1: ({ children }) => (
            <h1 style={{
              fontSize: '20px',
              fontWeight: '600',
              margin: '0 0 16px 0',
              color: '#24292e',
            }}>
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 style={{
              fontSize: '18px',
              fontWeight: '600',
              margin: '0 0 12px 0',
              color: '#24292e',
            }}>
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 style={{
              fontSize: '16px',
              fontWeight: '600',
              margin: '0 0 12px 0',
              color: '#24292e',
            }}>
              {children}
            </h3>
          ),
        }}
      >
        {message.content}
      </ReactMarkdown>
    ) : message.content,
    placement: message.role === 'user' ? ('end' as const) : ('start' as const),
    avatar: message.role === 'user' ? { style: { backgroundColor: '#1677ff' } } : undefined,
    loading: message.role === 'assistant' && isLoading && message === messages[messages.length - 1],
    variant: message.role === 'assistant' ? 'shadow' : undefined,
  }));

  return (
    <div
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        padding: '24px',
        gap: '16px',
      }}
    >
      {/* Chat Messages Area */}
      <div
        style={{
          flex: 1,
          overflow: 'auto',
          padding: '16px',
          border: '1px solid #f0f0f0',
          borderRadius: '8px',
          backgroundColor: '#fafafa',
        }}
      >
        {messages.length === 0 ? (
          <div
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Empty
              description="Start a conversation"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          <Bubble.List
            items={bubbleItems}
            style={{ height: '100%' }}
          />
        )}

        {isLoading && messages.length > 0 && (
          <div style={{ textAlign: 'center', padding: '16px' }}>
            <Spin size="small" />
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          closable
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* Message Input */}
      <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '16px' }}>
        <Sender
          ref={senderRef}
          placeholder="Type your message here..."
          value={inputValue}
          onChange={setInputValue}
          onSubmit={handleSendMessage}
          loading={isLoading}
          style={{
            borderRadius: '8px',
          }}
        />
      </div>
    </div>
  );
}
