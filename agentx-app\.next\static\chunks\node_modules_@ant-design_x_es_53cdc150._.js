(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@ant-design/x/es/_util/hooks/use-x-component-config.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/x-provider/context.js [app-client] (ecmascript)");
;
;
const defaultXComponentStyleConfig = {
    classNames: {},
    styles: {},
    className: '',
    style: {}
};
const useXComponentConfig = (component)=>{
    const xProviderContext = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useContext(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "useXComponentConfig.useMemo": ()=>({
                ...defaultXComponentStyleConfig,
                ...xProviderContext[component]
            })
    }["useXComponentConfig.useMemo"], [
        xProviderContext[component]
    ]);
};
const __TURBOPACK__default__export__ = useXComponentConfig;
}}),
"[project]/node_modules/@ant-design/x/es/x-provider/hooks/use-x-provider-context.js [app-client] (ecmascript) <export default as useXProviderContext>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useXProviderContext": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/x-provider/hooks/use-x-provider-context.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/@ant-design/x/es/bubble/hooks/useTypedEffect.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
function isString(str) {
    return typeof str === 'string';
}
/**
 * Return typed content and typing status when typing is enabled.
 * Or return content directly.
 */ const useTypedEffect = (content, typingEnabled, typingStep, typingInterval)=>{
    const prevContentRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])('');
    const [typingIndex, setTypingIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const mergedTypingEnabled = typingEnabled && isString(content);
    // Reset typing index when content changed
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "useTypedEffect.useLayoutEffect": ()=>{
            if (!mergedTypingEnabled && isString(content)) {
                setTypingIndex(content.length);
            } else if (isString(content) && isString(prevContentRef.current) && content.indexOf(prevContentRef.current) !== 0) {
                setTypingIndex(1);
            }
            prevContentRef.current = content;
        }
    }["useTypedEffect.useLayoutEffect"], [
        content
    ]);
    // Start typing
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useTypedEffect.useEffect": ()=>{
            if (mergedTypingEnabled && typingIndex < content.length) {
                const id = setTimeout({
                    "useTypedEffect.useEffect.id": ()=>{
                        setTypingIndex({
                            "useTypedEffect.useEffect.id": (prev)=>prev + typingStep
                        }["useTypedEffect.useEffect.id"]);
                    }
                }["useTypedEffect.useEffect.id"], typingInterval);
                return ({
                    "useTypedEffect.useEffect": ()=>{
                        clearTimeout(id);
                    }
                })["useTypedEffect.useEffect"];
            }
        }
    }["useTypedEffect.useEffect"], [
        typingIndex,
        typingEnabled,
        content
    ]);
    const mergedTypingContent = mergedTypingEnabled ? content.slice(0, typingIndex) : content;
    return [
        mergedTypingContent,
        mergedTypingEnabled && typingIndex < content.length
    ];
};
const __TURBOPACK__default__export__ = useTypedEffect;
}}),
"[project]/node_modules/@ant-design/x/es/bubble/hooks/useTypingConfig.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function useTypingConfig(typing) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useTypingConfig.useMemo": ()=>{
            if (!typing) {
                return [
                    false,
                    0,
                    0,
                    null
                ];
            }
            let baseConfig = {
                step: 1,
                interval: 50,
                // set default suffix is empty
                suffix: null
            };
            if (typeof typing === 'object') {
                baseConfig = {
                    ...baseConfig,
                    ...typing
                };
            }
            return [
                true,
                baseConfig.step,
                baseConfig.interval,
                baseConfig.suffix
            ];
        }
    }["useTypingConfig.useMemo"], [
        typing
    ]);
}
const __TURBOPACK__default__export__ = useTypingConfig;
}}),
"[project]/node_modules/@ant-design/x/es/bubble/loading.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const Loading = ({ prefixCls })=>/*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: `${prefixCls}-dot`
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("i", {
        className: `${prefixCls}-dot-item`,
        key: `item-${1}`
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("i", {
        className: `${prefixCls}-dot-item`,
        key: `item-${2}`
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("i", {
        className: `${prefixCls}-dot-item`,
        key: `item-${3}`
    }));
const __TURBOPACK__default__export__ = Loading;
}}),
"[project]/node_modules/@ant-design/x/es/version/version.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = '1.3.0';
}}),
"[project]/node_modules/@ant-design/x/es/version/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// @ts-ignore
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$version$2f$version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/version/version.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$version$2f$version$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}}),
"[project]/node_modules/@ant-design/x/es/theme/useToken.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useToken),
    "getComputedToken": (()=>getComputedToken),
    "useInternalToken": (()=>useInternalToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$theme$2f$createTheme$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__createTheme$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/theme/createTheme.js [app-client] (ecmascript) <export default as createTheme>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$hooks$2f$useCacheToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useCacheToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/hooks/useCacheToken.js [app-client] (ecmascript) <export default as useCacheToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__theme$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/index.js [app-client] (ecmascript) <export default as theme>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/useToken.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$alias$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/alias.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$version$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/version/index.js [app-client] (ecmascript)");
;
;
;
;
;
;
const defaultTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$theme$2f$createTheme$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__createTheme$3e$__["createTheme"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__theme$3e$__["theme"].defaultAlgorithm);
const preserve = {
    screenXS: true,
    screenXSMin: true,
    screenXSMax: true,
    screenSM: true,
    screenSMMin: true,
    screenSMMax: true,
    screenMD: true,
    screenMDMin: true,
    screenMDMax: true,
    screenLG: true,
    screenLGMin: true,
    screenLGMax: true,
    screenXL: true,
    screenXLMin: true,
    screenXLMax: true,
    screenXXL: true,
    screenXXLMin: true
};
const getComputedToken = (originToken, overrideToken, theme)=>{
    const derivativeToken = theme.getDerivativeToken(originToken);
    const { override, ...components } = overrideToken;
    // Merge with override
    let mergedDerivativeToken = {
        ...derivativeToken,
        override
    };
    // Format if needed
    mergedDerivativeToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$alias$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(mergedDerivativeToken);
    if (components) {
        Object.entries(components).forEach(([key, value])=>{
            const { theme: componentTheme, ...componentTokens } = value;
            let mergedComponentToken = componentTokens;
            if (componentTheme) {
                mergedComponentToken = getComputedToken({
                    ...mergedDerivativeToken,
                    ...componentTokens
                }, {
                    override: componentTokens
                }, componentTheme);
            }
            mergedDerivativeToken[key] = mergedComponentToken;
        });
    }
    return mergedDerivativeToken;
};
function useInternalToken() {
    const { token: rootDesignToken, hashed, theme = defaultTheme, override, cssVar } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useContext(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__theme$3e$__["theme"]._internalContext);
    const [token, hashId, realToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$hooks$2f$useCacheToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useCacheToken$3e$__["useCacheToken"])(theme, [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__theme$3e$__["theme"].defaultSeed,
        rootDesignToken
    ], {
        salt: `${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$version$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]}-${hashed || ''}`,
        override,
        getComputedToken,
        cssVar: cssVar && {
            prefix: cssVar.prefix,
            key: cssVar.key,
            unitless: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unitless"],
            ignore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ignore"],
            preserve
        }
    });
    return [
        theme,
        realToken,
        hashed ? hashId : '',
        token,
        cssVar
    ];
}
function useToken() {
    const [theme, token, hashId] = useInternalToken();
    return {
        theme,
        token,
        hashId
    };
}
}}),
"[project]/node_modules/@ant-design/x/es/theme/genStyleUtils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "genComponentStyleHook": (()=>genComponentStyleHook),
    "genStyleHooks": (()=>genStyleHooks),
    "genSubStyleComponent": (()=>genSubStyleComponent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__genStyleUtils$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/genStyleUtils.js [app-client] (ecmascript) <export default as genStyleUtils>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useXProviderContext$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/x-provider/hooks/use-x-provider-context.js [app-client] (ecmascript) <export default as useXProviderContext>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/theme/useToken.js [app-client] (ecmascript)");
;
;
;
const { genStyleHooks, genComponentStyleHook, genSubStyleComponent } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__genStyleUtils$3e$__["genStyleUtils"])({
    usePrefix: ()=>{
        const { getPrefixCls, iconPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useXProviderContext$3e$__["useXProviderContext"])();
        return {
            iconPrefixCls,
            rootPrefixCls: getPrefixCls()
        };
    },
    useToken: ()=>{
        const [theme, realToken, hashId, token, cssVar] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useInternalToken"])();
        return {
            theme,
            realToken,
            hashId,
            token,
            cssVar
        };
    },
    useCSP: ()=>{
        const { csp } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useXProviderContext$3e$__["useXProviderContext"])();
        return csp ?? {};
    },
    layer: {
        name: 'antdx',
        dependencies: [
            'antd'
        ]
    }
});
}}),
"[project]/node_modules/@ant-design/x/es/bubble/style/content.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "genShapeStyle": (()=>genShapeStyle),
    "genVariantStyle": (()=>genVariantStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
;
const genVariantStyle = (token)=>{
    const { componentCls, paddingSM, padding } = token;
    return {
        [componentCls]: {
            [`${componentCls}-content`]: {
                // Shared: filled, outlined, shadow
                '&-filled,&-outlined,&-shadow': {
                    padding: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(paddingSM)} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(padding)}`,
                    borderRadius: token.borderRadiusLG
                },
                // Filled:
                '&-filled': {
                    backgroundColor: token.colorFillContent
                },
                // Outlined:
                '&-outlined': {
                    border: `1px solid ${token.colorBorderSecondary}`
                },
                // Shadow:
                '&-shadow': {
                    boxShadow: token.boxShadowTertiary
                }
            }
        }
    };
};
const genShapeStyle = (token)=>{
    const { componentCls, fontSize, lineHeight, paddingSM, padding, calc } = token;
    const halfRadius = calc(fontSize).mul(lineHeight).div(2).add(paddingSM).equal();
    const contentCls = `${componentCls}-content`;
    return {
        [componentCls]: {
            [contentCls]: {
                // round:
                '&-round': {
                    borderRadius: {
                        _skip_check_: true,
                        value: halfRadius
                    },
                    paddingInline: calc(padding).mul(1.25).equal()
                }
            },
            // corner:
            [`&-start ${contentCls}-corner`]: {
                borderStartStartRadius: token.borderRadiusXS
            },
            [`&-end ${contentCls}-corner`]: {
                borderStartEndRadius: token.borderRadiusXS
            }
        }
    };
};
}}),
"[project]/node_modules/@ant-design/x/es/bubble/style/list.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const genBubbleListStyle = (token)=>{
    const { componentCls, padding } = token;
    return {
        [`${componentCls}-list`]: {
            display: 'flex',
            flexDirection: 'column',
            gap: padding,
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
                width: 8,
                backgroundColor: 'transparent'
            },
            '&::-webkit-scrollbar-thumb': {
                backgroundColor: token.colorTextTertiary,
                borderRadius: token.borderRadiusSM
            },
            // For Firefox
            '&': {
                scrollbarWidth: 'thin',
                scrollbarColor: `${token.colorTextTertiary} transparent`
            }
        }
    };
};
const __TURBOPACK__default__export__ = genBubbleListStyle;
}}),
"[project]/node_modules/@ant-design/x/es/bubble/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "prepareComponentToken": (()=>prepareComponentToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/Keyframes.js [app-client] (ecmascript) <export default as Keyframes>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$theme$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/theme/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$style$2f$content$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/style/content.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$style$2f$list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/style/list.js [app-client] (ecmascript)");
;
;
;
;
;
const loadingMove = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__["Keyframes"]('loadingMove', {
    '0%': {
        transform: 'translateY(0)'
    },
    '10%': {
        transform: 'translateY(4px)'
    },
    '20%': {
        transform: 'translateY(0)'
    },
    '30%': {
        transform: 'translateY(-4px)'
    },
    '40%': {
        transform: 'translateY(0)'
    }
});
const cursorBlink = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__["Keyframes"]('cursorBlink', {
    '0%': {
        opacity: 1
    },
    '50%': {
        opacity: 0
    },
    '100%': {
        opacity: 1
    }
});
// biome-ignore lint/suspicious/noEmptyInterface: ComponentToken need to be empty by default
const genBubbleStyle = (token)=>{
    const { componentCls, fontSize, lineHeight, paddingSM, colorText, calc } = token;
    return {
        [componentCls]: {
            display: 'flex',
            columnGap: paddingSM,
            [`&${componentCls}-end`]: {
                justifyContent: 'end',
                flexDirection: 'row-reverse',
                [`& ${componentCls}-content-wrapper`]: {
                    alignItems: 'flex-end'
                }
            },
            [`&${componentCls}-rtl`]: {
                direction: 'rtl'
            },
            [`&${componentCls}-typing ${componentCls}-content:last-child::after`]: {
                content: '"|"',
                fontWeight: 900,
                userSelect: 'none',
                opacity: 1,
                marginInlineStart: '0.1em',
                animationName: cursorBlink,
                animationDuration: '0.8s',
                animationIterationCount: 'infinite',
                animationTimingFunction: 'linear'
            },
            // ============================ Avatar =============================
            [`& ${componentCls}-avatar`]: {
                display: 'inline-flex',
                justifyContent: 'center',
                alignSelf: 'flex-start'
            },
            // ======================== Header & Footer ========================
            [`& ${componentCls}-header, & ${componentCls}-footer`]: {
                fontSize: fontSize,
                lineHeight: lineHeight,
                color: token.colorText
            },
            [`& ${componentCls}-header`]: {
                marginBottom: token.paddingXXS
            },
            [`& ${componentCls}-footer`]: {
                marginTop: paddingSM
            },
            // =========================== Content =============================
            [`& ${componentCls}-content-wrapper`]: {
                flex: 'auto',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                minWidth: 0,
                maxWidth: '100%'
            },
            [`& ${componentCls}-content`]: {
                position: 'relative',
                boxSizing: 'border-box',
                minWidth: 0,
                maxWidth: '100%',
                color: colorText,
                fontSize: token.fontSize,
                lineHeight: token.lineHeight,
                minHeight: calc(paddingSM).mul(2).add(calc(lineHeight).mul(fontSize)).equal(),
                wordBreak: 'break-word',
                [`& ${componentCls}-dot`]: {
                    position: 'relative',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    columnGap: token.marginXS,
                    padding: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.paddingXXS)}`,
                    '&-item': {
                        backgroundColor: token.colorPrimary,
                        borderRadius: '100%',
                        width: 4,
                        height: 4,
                        animationName: loadingMove,
                        animationDuration: '2s',
                        animationIterationCount: 'infinite',
                        animationTimingFunction: 'linear',
                        '&:nth-child(1)': {
                            animationDelay: '0s'
                        },
                        '&:nth-child(2)': {
                            animationDelay: '0.2s'
                        },
                        '&:nth-child(3)': {
                            animationDelay: '0.4s'
                        }
                    }
                }
            }
        }
    };
};
const prepareComponentToken = ()=>({});
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$theme$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Bubble', (token)=>{
    const bubbleToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {});
    return [
        genBubbleStyle(bubbleToken),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$style$2f$list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(bubbleToken),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$style$2f$content$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genVariantStyle"])(bubbleToken),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$style$2f$content$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genShapeStyle"])(bubbleToken)
    ];
}, prepareComponentToken);
}}),
"[project]/node_modules/@ant-design/x/es/bubble/Bubble.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BubbleContext": (()=>BubbleContext),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$avatar$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Avatar$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/avatar/index.js [app-client] (ecmascript) <export default as Avatar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$_util$2f$hooks$2f$use$2d$x$2d$component$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/_util/hooks/use-x-component-config.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useXProviderContext$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/x-provider/hooks/use-x-provider-context.js [app-client] (ecmascript) <export default as useXProviderContext>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$hooks$2f$useTypedEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/hooks/useTypedEffect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$hooks$2f$useTypingConfig$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/hooks/useTypingConfig.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$loading$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/loading.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/style/index.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
const BubbleContext = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createContext({});
const Bubble = (props, ref)=>{
    const { prefixCls: customizePrefixCls, className, rootClassName, style, classNames = {}, styles = {}, avatar, placement = 'start', loading = false, loadingRender, typing, content = '', messageRender, variant = 'filled', shape, onTypingComplete, header, footer, _key, ...otherHtmlProps } = props;
    const { onUpdate } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useContext(BubbleContext);
    // ============================= Refs =============================
    const divRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(null);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useImperativeHandle(ref, {
        "Bubble.useImperativeHandle": ()=>({
                nativeElement: divRef.current
            })
    }["Bubble.useImperativeHandle"]);
    // ============================ Prefix ============================
    const { direction, getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useXProviderContext$3e$__["useXProviderContext"])();
    const prefixCls = getPrefixCls('bubble', customizePrefixCls);
    // ===================== Component Config =========================
    const contextConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$_util$2f$hooks$2f$use$2d$x$2d$component$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])('bubble');
    // ============================ Typing ============================
    const [typingEnabled, typingStep, typingInterval, customSuffix] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$hooks$2f$useTypingConfig$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(typing);
    const [typedContent, isTyping] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$hooks$2f$useTypedEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(content, typingEnabled, typingStep, typingInterval);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "Bubble.useEffect": ()=>{
            onUpdate?.();
        }
    }["Bubble.useEffect"], [
        typedContent
    ]);
    const triggerTypingCompleteRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(false);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "Bubble.useEffect": ()=>{
            if (!isTyping && !loading) {
                // StrictMode will trigger this twice,
                // So we need a flag to avoid that
                if (!triggerTypingCompleteRef.current) {
                    triggerTypingCompleteRef.current = true;
                    onTypingComplete?.();
                }
            } else {
                triggerTypingCompleteRef.current = false;
            }
        }
    }["Bubble.useEffect"], [
        isTyping,
        loading
    ]);
    // ============================ Styles ============================
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const mergedCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, rootClassName, contextConfig.className, className, hashId, cssVarCls, `${prefixCls}-${placement}`, {
        [`${prefixCls}-rtl`]: direction === 'rtl',
        [`${prefixCls}-typing`]: isTyping && !loading && !messageRender && !customSuffix
    });
    // ============================ Avatar ============================
    const avatarNode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "Bubble.useMemo[avatarNode]": ()=>/*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isValidElement(avatar) ? avatar : /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$avatar$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Avatar$3e$__["Avatar"], avatar)
    }["Bubble.useMemo[avatarNode]"], [
        avatar
    ]);
    // =========================== Content ============================
    const mergedContent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "Bubble.useMemo[mergedContent]": ()=>messageRender ? messageRender(typedContent) : typedContent
    }["Bubble.useMemo[mergedContent]"], [
        typedContent,
        messageRender
    ]);
    const renderSlot = (node)=>typeof node === 'function' ? node(typedContent, {
            key: _key
        }) : node;
    // ============================ Render ============================
    let contentNode;
    if (loading) {
        contentNode = loadingRender ? loadingRender() : /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$loading$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            prefixCls: prefixCls
        });
    } else {
        contentNode = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, null, mergedContent, isTyping && customSuffix);
    }
    let fullContent = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        style: {
            ...contextConfig.styles.content,
            ...styles.content
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-content`, `${prefixCls}-content-${variant}`, shape && `${prefixCls}-content-${shape}`, contextConfig.classNames.content, classNames.content)
    }, contentNode);
    if (header || footer) {
        fullContent = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
            className: `${prefixCls}-content-wrapper`
        }, header && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-header`, contextConfig.classNames.header, classNames.header),
            style: {
                ...contextConfig.styles.header,
                ...styles.header
            }
        }, renderSlot(header)), fullContent, footer && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-footer`, contextConfig.classNames.footer, classNames.footer),
            style: {
                ...contextConfig.styles.footer,
                ...styles.footer
            }
        }, renderSlot(footer)));
    }
    return wrapCSSVar(/*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        style: {
            ...contextConfig.style,
            ...style
        },
        className: mergedCls
    }, otherHtmlProps, {
        ref: divRef
    }), avatar && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        style: {
            ...contextConfig.styles.avatar,
            ...styles.avatar
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-avatar`, contextConfig.classNames.avatar, classNames.avatar)
    }, avatarNode), fullContent));
};
const ForwardBubble = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(Bubble);
if ("TURBOPACK compile-time truthy", 1) {
    ForwardBubble.displayName = 'Bubble';
}
const __TURBOPACK__default__export__ = ForwardBubble;
}}),
"[project]/node_modules/@ant-design/x/es/bubble/hooks/useListData.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useListData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function useListData(items, roles) {
    const getRoleBubbleProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useListData.useCallback[getRoleBubbleProps]": (bubble, index)=>{
            if (typeof roles === 'function') {
                return roles(bubble, index);
            }
            if (roles) {
                return roles[bubble.role] || {};
            }
            return {};
        }
    }["useListData.useCallback[getRoleBubbleProps]"], [
        roles
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useListData.useMemo": ()=>(items || []).map({
                "useListData.useMemo": (bubbleData, i)=>{
                    const mergedKey = bubbleData.key ?? `preset_${i}`;
                    return {
                        ...getRoleBubbleProps(bubbleData, i),
                        ...bubbleData,
                        key: mergedKey
                    };
                }
            }["useListData.useMemo"])
    }["useListData.useMemo"], [
        items,
        getRoleBubbleProps
    ]);
}
}}),
"[project]/node_modules/@ant-design/x/es/bubble/BubbleList.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-client] (ecmascript) <export default as useEvent>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/pickAttrs.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useXProviderContext$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/x-provider/hooks/use-x-provider-context.js [app-client] (ecmascript) <export default as useXProviderContext>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$Bubble$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/Bubble.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$hooks$2f$useListData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/hooks/useListData.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/style/index.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
const BubbleListItem = ({ _key, ...restProps }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$Bubble$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, restProps, {
        ref: (node)=>{
            if (node) {
                ref.current[_key] = node;
            } else {
                delete ref.current?.[_key];
            }
        }
    }));
const MemoBubbleListItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(BubbleListItem));
const TOLERANCE = 1;
const BubbleList = (props, ref)=>{
    const { prefixCls: customizePrefixCls, rootClassName, className, items, autoScroll = true, roles, ...restProps } = props;
    const domProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(restProps, {
        attr: true,
        aria: true
    });
    // ============================= Refs =============================
    const listRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const bubbleRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({});
    // ============================ Prefix ============================
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useXProviderContext$3e$__["useXProviderContext"])();
    const prefixCls = getPrefixCls('bubble', customizePrefixCls);
    const listPrefixCls = `${prefixCls}-list`;
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    // ============================ Typing ============================
    const [initialized, setInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BubbleList.useEffect": ()=>{
            setInitialized(true);
            return ({
                "BubbleList.useEffect": ()=>{
                    setInitialized(false);
                }
            })["BubbleList.useEffect"];
        }
    }["BubbleList.useEffect"], []);
    // ============================= Data =============================
    const mergedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$hooks$2f$useListData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(items, roles);
    // ============================ Scroll ============================
    // Is current scrollTop at the end. User scroll will make this false.
    const [scrollReachEnd, setScrollReachEnd] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [updateCount, setUpdateCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const onInternalScroll = (e)=>{
        const target = e.target;
        setScrollReachEnd(target.scrollHeight - Math.abs(target.scrollTop) - target.clientHeight <= TOLERANCE);
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BubbleList.useEffect": ()=>{
            if (autoScroll && listRef.current && scrollReachEnd) {
                listRef.current.scrollTo({
                    top: listRef.current.scrollHeight
                });
            }
        }
    }["BubbleList.useEffect"], [
        updateCount
    ]);
    // Always scroll to bottom when data change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BubbleList.useEffect": ()=>{
            if (autoScroll) {
                // New date come, the origin last one is the second last one
                const lastItemKey = mergedData[mergedData.length - 2]?.key;
                const bubbleInst = bubbleRefs.current[lastItemKey];
                // Auto scroll if last 2 item is visible
                if (bubbleInst) {
                    const { nativeElement } = bubbleInst;
                    const { top, bottom } = nativeElement.getBoundingClientRect();
                    const { top: listTop, bottom: listBottom } = listRef.current.getBoundingClientRect();
                    const isVisible = top < listBottom && bottom > listTop;
                    if (isVisible) {
                        setUpdateCount({
                            "BubbleList.useEffect": (c)=>c + 1
                        }["BubbleList.useEffect"]);
                        setScrollReachEnd(true);
                    }
                }
            }
        }
    }["BubbleList.useEffect"], [
        mergedData.length
    ]);
    // ========================== Outer Ref ===========================
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "BubbleList.useImperativeHandle": ()=>({
                nativeElement: listRef.current,
                scrollTo: ({
                    "BubbleList.useImperativeHandle": ({ key, offset, behavior = 'smooth', block })=>{
                        if (typeof offset === 'number') {
                            // Offset scroll
                            listRef.current.scrollTo({
                                top: offset,
                                behavior
                            });
                        } else if (key !== undefined) {
                            // Key scroll
                            const bubbleInst = bubbleRefs.current[key];
                            if (bubbleInst) {
                                // Block current auto scrolling
                                const index = mergedData.findIndex({
                                    "BubbleList.useImperativeHandle.index": (dataItem)=>dataItem.key === key
                                }["BubbleList.useImperativeHandle.index"]);
                                setScrollReachEnd(index === mergedData.length - 1);
                                // Do native scroll
                                bubbleInst.nativeElement.scrollIntoView({
                                    behavior,
                                    block
                                });
                            }
                        }
                    }
                })["BubbleList.useImperativeHandle"]
            })
    }["BubbleList.useImperativeHandle"]);
    // =========================== Context ============================
    // When bubble content update, we try to trigger `autoScroll` for sync
    const onBubbleUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__["useEvent"])({
        "BubbleList.useEvent[onBubbleUpdate]": ()=>{
            if (autoScroll) {
                setUpdateCount({
                    "BubbleList.useEvent[onBubbleUpdate]": (c)=>c + 1
                }["BubbleList.useEvent[onBubbleUpdate]"]);
            }
        }
    }["BubbleList.useEvent[onBubbleUpdate]"]);
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "BubbleList.useMemo[context]": ()=>({
                onUpdate: onBubbleUpdate
            })
    }["BubbleList.useMemo[context]"], []);
    // ============================ Render ============================
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$Bubble$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BubbleContext"].Provider, {
        value: context
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, domProps, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(listPrefixCls, rootClassName, className, hashId, cssVarCls, {
            [`${listPrefixCls}-reach-end`]: scrollReachEnd
        }),
        ref: listRef,
        onScroll: onInternalScroll
    }), mergedData.map(({ key, ...bubble })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(MemoBubbleListItem, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, bubble, {
            key: key,
            _key: key,
            ref: bubbleRefs,
            typing: initialized ? bubble.typing : false
        }))))));
};
const ForwardBubbleList = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(BubbleList);
if ("TURBOPACK compile-time truthy", 1) {
    ForwardBubbleList.displayName = 'BubbleList';
}
const __TURBOPACK__default__export__ = ForwardBubbleList;
}}),
"[project]/node_modules/@ant-design/x/es/bubble/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$Bubble$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/Bubble.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$BubbleList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/BubbleList.js [app-client] (ecmascript)");
;
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$Bubble$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].List = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$BubbleList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$Bubble$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}}),
"[project]/node_modules/@ant-design/x/es/bubble/index.js [app-client] (ecmascript) <export default as Bubble>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Bubble": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$bubble$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/bubble/index.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/@ant-design/x/es/_util/hooks/use-proxy-imperative-handle.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Proxy the dom ref with `{ nativeElement, otherFn }` type
// ref: https://github.com/ant-design/ant-design/discussions/45242
__turbopack_context__.s({
    "default": (()=>useProxyImperativeHandle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function useProxyImperativeHandle(ref, init) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "useProxyImperativeHandle.useImperativeHandle": ()=>{
            const refObj = init();
            const { nativeElement } = refObj;
            return new Proxy(nativeElement, {
                get (obj, prop) {
                    if (refObj[prop]) {
                        return refObj[prop];
                    }
                    return Reflect.get(obj, prop);
                }
            });
        }
    }["useProxyImperativeHandle.useImperativeHandle"]);
}
}}),
"[project]/node_modules/@ant-design/x/es/sender/SenderHeader.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SendHeaderContext": (()=>SendHeaderContext),
    "default": (()=>SenderHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CloseOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/CloseOutlined.js [app-client] (ecmascript) <export default as CloseOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals> <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-motion/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-motion/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
;
;
const SendHeaderContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({});
const collapseHeight = ()=>({
        height: 0
    });
const expandedHeight = (ele)=>({
        height: ele.scrollHeight
    });
function SenderHeader(props) {
    const { title, onOpenChange, open, children, className, style, classNames: classes = {}, styles = {}, closable, forceRender } = props;
    const { prefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SendHeaderContext);
    const headerCls = `${prefixCls}-header`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
        motionEnter: true,
        motionLeave: true,
        motionName: `${headerCls}-motion`,
        leavedClassName: `${headerCls}-motion-hidden`,
        onEnterStart: collapseHeight,
        onEnterActive: expandedHeight,
        onLeaveStart: expandedHeight,
        onLeaveActive: collapseHeight,
        visible: open,
        forceRender: forceRender
    }, ({ className: motionClassName, style: motionStyle })=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(headerCls, motionClassName, className),
            style: {
                ...motionStyle,
                ...style
            }
        }, (closable !== false || title) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
            className: // We follow antd naming standard here.
            // So the header part is use `-header` suffix.
            // Though its little bit weird for double `-header`.
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${headerCls}-header`, classes.header),
            style: {
                ...styles.header
            }
        }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
            className: `${headerCls}-title`
        }, title), closable !== false && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
            className: `${headerCls}-close`
        }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
            type: "text",
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CloseOutlined$3e$__["CloseOutlined"], null),
            size: "small",
            onClick: ()=>{
                onOpenChange?.(!open);
            }
        }))), children && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${headerCls}-content`, classes.content),
            style: {
                ...styles.content
            }
        }, children));
    });
}
}}),
"[project]/node_modules/@ant-design/x/es/sender/components/ActionButton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ActionButton": (()=>ActionButton),
    "ActionButtonContext": (()=>ActionButtonContext),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals> <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
;
const ActionButtonContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
function ActionButton(props, ref) {
    const { className, action, onClick, ...restProps } = props;
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ActionButtonContext);
    const { prefixCls, disabled: rootDisabled } = context;
    const mergedDisabled = restProps.disabled ?? rootDisabled ?? context[`${action}Disabled`];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        type: "text"
    }, restProps, {
        ref: ref,
        onClick: (e)=>{
            if (mergedDisabled) {
                return;
            }
            context[action]?.();
            onClick?.(e);
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, className, {
            [`${prefixCls}-disabled`]: mergedDisabled
        })
    }));
}
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(ActionButton);
}}),
"[project]/node_modules/@ant-design/x/es/sender/components/ClearButton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ClearOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ClearOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/ClearOutlined.js [app-client] (ecmascript) <export default as ClearOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/ActionButton.js [app-client] (ecmascript)");
;
;
;
;
function ClearButton(props, ref) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ClearOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ClearOutlined$3e$__["ClearOutlined"], null)
    }, props, {
        action: "onClear",
        ref: ref
    }));
}
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(ClearButton);
}}),
"[project]/node_modules/@ant-design/x/es/sender/StopLoading.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const StopLoadingIcon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])((props)=>{
    const { className } = props;
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        color: "currentColor",
        viewBox: "0 0 1000 1000",
        xmlns: "http://www.w3.org/2000/svg",
        className: className
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("title", null, "Stop Loading"), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("rect", {
        fill: "currentColor",
        height: "250",
        rx: "24",
        ry: "24",
        width: "250",
        x: "375",
        y: "375"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("circle", {
        cx: "500",
        cy: "500",
        fill: "none",
        r: "450",
        stroke: "currentColor",
        strokeWidth: "100",
        opacity: "0.45"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("circle", {
        cx: "500",
        cy: "500",
        fill: "none",
        r: "450",
        stroke: "currentColor",
        strokeWidth: "100",
        strokeDasharray: "600 9999999"
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("animateTransform", {
        attributeName: "transform",
        dur: "1s",
        from: "0 500 500",
        repeatCount: "indefinite",
        to: "360 500 500",
        type: "rotate"
    })));
});
const __TURBOPACK__default__export__ = StopLoadingIcon;
}}),
"[project]/node_modules/@ant-design/x/es/sender/components/LoadingButton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$StopLoading$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/StopLoading.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/ActionButton.js [app-client] (ecmascript)");
;
;
;
;
;
function LoadingButton(props, ref) {
    const { prefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ActionButtonContext"]);
    const { className } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        icon: null,
        color: "primary",
        variant: "text",
        shape: "circle"
    }, props, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(className, `${prefixCls}-loading-button`),
        action: "onCancel",
        ref: ref
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$StopLoading$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: `${prefixCls}-loading-icon`
    }));
}
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(LoadingButton);
}}),
"[project]/node_modules/@ant-design/x/es/sender/components/SendButton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ArrowUpOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUpOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/ArrowUpOutlined.js [app-client] (ecmascript) <export default as ArrowUpOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/ActionButton.js [app-client] (ecmascript)");
;
;
;
;
function SendButton(props, ref) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ArrowUpOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUpOutlined$3e$__["ArrowUpOutlined"], null),
        type: "primary",
        shape: "circle"
    }, props, {
        action: "onSend",
        ref: ref
    }));
}
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(SendButton);
}}),
"[project]/node_modules/@ant-design/x/es/sender/components/SpeechButton/RecordingIcon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RecordingIcon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const SIZE = 1000;
const COUNT = 4;
const RECT_WIDTH = 140;
const RECT_RADIUS = RECT_WIDTH / 2;
const RECT_HEIGHT_MIN = 250;
const RECT_HEIGHT_MAX = 500;
const DURATION = 0.8;
function RecordingIcon({ className }) {
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        color: "currentColor",
        viewBox: `0 0 ${SIZE} ${SIZE}`,
        xmlns: "http://www.w3.org/2000/svg",
        className: className
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("title", null, "Speech Recording"), Array.from({
        length: COUNT
    }).map((_, index)=>{
        const dest = (SIZE - RECT_WIDTH * COUNT) / (COUNT - 1);
        const x = index * (dest + RECT_WIDTH);
        const yMin = SIZE / 2 - RECT_HEIGHT_MIN / 2;
        const yMax = SIZE / 2 - RECT_HEIGHT_MAX / 2;
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("rect", {
            fill: "currentColor",
            rx: RECT_RADIUS,
            ry: RECT_RADIUS,
            height: RECT_HEIGHT_MIN,
            width: RECT_WIDTH,
            x: x,
            y: yMin,
            key: index
        }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("animate", {
            attributeName: "height",
            values: `${RECT_HEIGHT_MIN}; ${RECT_HEIGHT_MAX}; ${RECT_HEIGHT_MIN}`,
            keyTimes: "0; 0.5; 1",
            dur: `${DURATION}s`,
            begin: `${DURATION / COUNT * index}s`,
            repeatCount: "indefinite"
        }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("animate", {
            attributeName: "y",
            values: `${yMin}; ${yMax}; ${yMin}`,
            keyTimes: "0; 0.5; 1",
            dur: `${DURATION}s`,
            begin: `${DURATION / COUNT * index}s`,
            repeatCount: "indefinite"
        }));
    }));
}
}}),
"[project]/node_modules/@ant-design/x/es/sender/components/SpeechButton/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$AudioMutedOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AudioMutedOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/AudioMutedOutlined.js [app-client] (ecmascript) <export default as AudioMutedOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$AudioOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AudioOutlined$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/icons/es/icons/AudioOutlined.js [app-client] (ecmascript) <export default as AudioOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/ActionButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$SpeechButton$2f$RecordingIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/SpeechButton/RecordingIcon.js [app-client] (ecmascript)");
;
;
;
;
;
function SpeechButton(props, ref) {
    const { speechRecording, onSpeechDisabled, prefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ActionButtonContext"]);
    let icon = null;
    if (speechRecording) {
        icon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$SpeechButton$2f$RecordingIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            className: `${prefixCls}-recording-icon`
        });
    } else if (onSpeechDisabled) {
        icon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$AudioMutedOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AudioMutedOutlined$3e$__["AudioMutedOutlined"], null);
    } else {
        icon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$AudioOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AudioOutlined$3e$__["AudioOutlined"], null);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        icon: icon,
        color: "primary",
        variant: "text"
    }, props, {
        action: "onSpeech",
        ref: ref
    }));
}
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(SpeechButton);
}}),
"[project]/node_modules/@ant-design/x/es/sender/style/header.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const genSenderHeaderStyle = (token)=>{
    const { componentCls, calc } = token;
    const headerCls = `${componentCls}-header`;
    return {
        [componentCls]: {
            [headerCls]: {
                borderBottomWidth: token.lineWidth,
                borderBottomStyle: 'solid',
                borderBottomColor: token.colorBorder,
                // ======================== Header ========================
                '&-header': {
                    background: token.colorFillAlter,
                    fontSize: token.fontSize,
                    lineHeight: token.lineHeight,
                    paddingBlock: calc(token.paddingSM).sub(token.lineWidthBold).equal(),
                    paddingInlineStart: token.padding,
                    paddingInlineEnd: token.paddingXS,
                    display: 'flex',
                    borderRadius: {
                        _skip_check_: true,
                        value: calc(token.borderRadius).mul(2).equal()
                    },
                    borderEndStartRadius: 0,
                    borderEndEndRadius: 0,
                    [`${headerCls}-title`]: {
                        flex: 'auto'
                    }
                },
                // ======================= Content ========================
                '&-content': {
                    padding: token.padding
                },
                // ======================== Motion ========================
                '&-motion': {
                    transition: [
                        'height',
                        'border'
                    ].map((prop)=>`${prop} ${token.motionDurationSlow}`).join(','),
                    overflow: 'hidden',
                    '&-enter-start, &-leave-active': {
                        borderBottomColor: 'transparent'
                    },
                    '&-hidden': {
                        display: 'none'
                    }
                }
            }
        }
    };
};
const __TURBOPACK__default__export__ = genSenderHeaderStyle;
}}),
"[project]/node_modules/@ant-design/x/es/sender/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "prepareComponentToken": (()=>prepareComponentToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$theme$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/theme/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$style$2f$header$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/style/header.js [app-client] (ecmascript)");
;
;
;
;
// biome-ignore lint/suspicious/noEmptyInterface: ComponentToken need to be empty by default
const genSenderStyle = (token)=>{
    const { componentCls, padding, paddingSM, paddingXS, paddingXXS, lineWidth, lineWidthBold, calc } = token;
    return {
        [componentCls]: {
            position: 'relative',
            width: '100%',
            boxSizing: 'border-box',
            boxShadow: `${token.boxShadowTertiary}`,
            transition: `background ${token.motionDurationSlow}`,
            // Border
            borderRadius: {
                _skip_check_: true,
                value: calc(token.borderRadius).mul(2).equal()
            },
            borderColor: token.colorBorder,
            borderWidth: 0,
            borderStyle: 'solid',
            // Border
            '&:after': {
                content: '""',
                position: 'absolute',
                inset: 0,
                pointerEvents: 'none',
                transition: `border-color ${token.motionDurationSlow}`,
                borderRadius: {
                    _skip_check_: true,
                    value: 'inherit'
                },
                borderStyle: 'inherit',
                borderColor: 'inherit',
                borderWidth: lineWidth
            },
            // Focus
            '&:focus-within': {
                boxShadow: `${token.boxShadowSecondary}`,
                borderColor: token.colorPrimary,
                '&:after': {
                    borderWidth: lineWidthBold
                }
            },
            '&-disabled': {
                background: token.colorBgContainerDisabled
            },
            // ============================== RTL ==============================
            [`&${componentCls}-rtl`]: {
                direction: 'rtl'
            },
            // ============================ Content ============================
            [`${componentCls}-content`]: {
                display: 'flex',
                gap: paddingXS,
                width: '100%',
                paddingBlock: paddingSM,
                paddingInlineStart: padding,
                paddingInlineEnd: paddingSM,
                boxSizing: 'border-box',
                alignItems: 'flex-end'
            },
            // ============================ Prefix =============================
            [`${componentCls}-prefix`]: {
                flex: 'none'
            },
            // ============================= Input =============================
            [`${componentCls}-input`]: {
                padding: 0,
                borderRadius: 0,
                flex: 'auto',
                alignSelf: 'center',
                minHeight: 'auto'
            },
            // ============================ Actions ============================
            [`${componentCls}-actions-list`]: {
                flex: 'none',
                display: 'flex',
                '&-presets': {
                    gap: token.paddingXS
                }
            },
            [`${componentCls}-actions-btn`]: {
                '&-disabled': {
                    opacity: 0.45
                },
                '&-loading-button': {
                    padding: 0,
                    border: 0
                },
                '&-loading-icon': {
                    height: token.controlHeight,
                    width: token.controlHeight,
                    verticalAlign: 'top'
                },
                '&-recording-icon': {
                    height: '1.2em',
                    width: '1.2em',
                    verticalAlign: 'top'
                }
            },
            // ============================ Footer =============================
            [`${componentCls}-footer`]: {
                paddingInlineStart: padding,
                paddingInlineEnd: paddingSM,
                paddingBlockEnd: paddingSM,
                paddingBlockStart: paddingXXS,
                boxSizing: 'border-box'
            }
        }
    };
};
const prepareComponentToken = ()=>({});
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$theme$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Sender', (token)=>{
    const { paddingXS, calc } = token;
    const SenderToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        SenderContentMaxWidth: `calc(100% - ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(calc(paddingXS).add(32).equal())})`
    });
    return [
        genSenderStyle(SenderToken),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$style$2f$header$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(SenderToken)
    ];
}, prepareComponentToken);
}}),
"[project]/node_modules/@ant-design/x/es/sender/useSpeech.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useSpeech)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-client] (ecmascript) <export default as useEvent>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMergedState$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useMergedState.js [app-client] (ecmascript) <export default as useMergedState>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
// Ensure that the SpeechRecognition API is available in the browser
let SpeechRecognition;
if (!SpeechRecognition && typeof window !== 'undefined') {
    SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
}
function useSpeech(onSpeech, allowSpeech) {
    const onEventSpeech = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__["useEvent"])(onSpeech);
    // ========================== Speech Config ==========================
    const [controlledRecording, onControlledRecordingChange, speechInControlled] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "useSpeech.useMemo": ()=>{
            if (typeof allowSpeech === 'object') {
                return [
                    allowSpeech.recording,
                    allowSpeech.onRecordingChange,
                    typeof allowSpeech.recording === 'boolean'
                ];
            }
            return [
                undefined,
                undefined,
                false
            ];
        }
    }["useSpeech.useMemo"], [
        allowSpeech
    ]);
    // ======================== Speech Permission ========================
    const [permissionState, setPermissionState] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "useSpeech.useEffect": ()=>{
            if (typeof navigator !== 'undefined' && 'permissions' in navigator) {
                let lastPermission = null;
                navigator.permissions.query({
                    name: 'microphone'
                }).then({
                    "useSpeech.useEffect": (permissionStatus)=>{
                        setPermissionState(permissionStatus.state);
                        // Keep the last permission status.
                        permissionStatus.onchange = ({
                            "useSpeech.useEffect": function() {
                                setPermissionState(this.state);
                            }
                        })["useSpeech.useEffect"];
                        lastPermission = permissionStatus;
                    }
                }["useSpeech.useEffect"]);
                return ({
                    "useSpeech.useEffect": ()=>{
                        // Avoid memory leaks
                        if (lastPermission) {
                            lastPermission.onchange = null;
                        }
                    }
                })["useSpeech.useEffect"];
            }
        }
    }["useSpeech.useEffect"], []);
    // Convert permission state to a simple type
    const mergedAllowSpeech = SpeechRecognition && permissionState !== 'denied';
    // ========================== Speech Events ==========================
    const recognitionRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(null);
    const [recording, setRecording] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMergedState$3e$__["useMergedState"])(false, {
        value: controlledRecording
    });
    const forceBreakRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(false);
    const ensureRecognition = ()=>{
        if (mergedAllowSpeech && !recognitionRef.current) {
            const recognition = new SpeechRecognition();
            recognition.onstart = ()=>{
                setRecording(true);
            };
            recognition.onend = ()=>{
                setRecording(false);
            };
            recognition.onresult = (event)=>{
                if (!forceBreakRef.current) {
                    const transcript = event.results?.[0]?.[0]?.transcript;
                    onEventSpeech(transcript);
                }
                forceBreakRef.current = false;
            };
            recognitionRef.current = recognition;
        }
    };
    const triggerSpeech = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__["useEvent"])({
        "useSpeech.useEvent[triggerSpeech]": (forceBreak)=>{
            // Ignore if `forceBreak` but is not recording
            if (forceBreak && !recording) {
                return;
            }
            forceBreakRef.current = forceBreak;
            if (speechInControlled) {
                // If in controlled mode, do nothing
                onControlledRecordingChange?.(!recording);
            } else {
                ensureRecognition();
                if (recognitionRef.current) {
                    if (recording) {
                        recognitionRef.current.stop();
                        onControlledRecordingChange?.(false);
                    } else {
                        recognitionRef.current.start();
                        onControlledRecordingChange?.(true);
                    }
                }
            }
        }
    }["useSpeech.useEvent[triggerSpeech]"]);
    return [
        mergedAllowSpeech,
        triggerSpeech,
        recording
    ];
}
}}),
"[project]/node_modules/@ant-design/x/es/sender/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$flex$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Flex$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/flex/index.js [app-client] (ecmascript) <export default as Flex>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/input/index.js [app-client] (ecmascript) <export default as Input>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMergedState$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useMergedState.js [app-client] (ecmascript) <export default as useMergedState>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/pickAttrs.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/utils/get.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$_util$2f$hooks$2f$use$2d$proxy$2d$imperative$2d$handle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/_util/hooks/use-proxy-imperative-handle.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$_util$2f$hooks$2f$use$2d$x$2d$component$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/_util/hooks/use-x-component-config.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useXProviderContext$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/x-provider/hooks/use-x-provider-context.js [app-client] (ecmascript) <export default as useXProviderContext>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$SenderHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/SenderHeader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/ActionButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ClearButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/ClearButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$LoadingButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/LoadingButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$SendButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/SendButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$SpeechButton$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/components/SpeechButton/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$useSpeech$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/useSpeech.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function getComponent(components, path, defaultComponent) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(components, path) || defaultComponent;
}
/** Used for actions render needed components */ const sharedRenderComponents = {
    SendButton: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$SendButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ClearButton: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ClearButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    LoadingButton: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$LoadingButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    SpeechButton: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$SpeechButton$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
};
const ForwardSender = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef((props, ref)=>{
    const { prefixCls: customizePrefixCls, styles = {}, classNames = {}, className, rootClassName, style, defaultValue, value, readOnly, submitType = 'enter', onSubmit, loading, components, onCancel, onChange, actions, onKeyPress, onKeyDown, disabled, allowSpeech, prefix, footer, header, onPaste, onPasteFile, autoSize = {
        maxRows: 8
    }, ...rest } = props;
    // ============================= MISC =============================
    const { direction, getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$x$2d$provider$2f$hooks$2f$use$2d$x$2d$provider$2d$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useXProviderContext$3e$__["useXProviderContext"])();
    const prefixCls = getPrefixCls('sender', customizePrefixCls);
    // ============================= Refs =============================
    const containerRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(null);
    const inputRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$_util$2f$hooks$2f$use$2d$proxy$2d$imperative$2d$handle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(ref, {
        "ForwardSender.useProxyImperativeHandle": ()=>({
                nativeElement: containerRef.current,
                focus: inputRef.current?.focus,
                blur: inputRef.current?.blur
            })
    }["ForwardSender.useProxyImperativeHandle"]);
    // ======================= Component Config =======================
    const contextConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$_util$2f$hooks$2f$use$2d$x$2d$component$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])('sender');
    const inputCls = `${prefixCls}-input`;
    // ============================ Styles ============================
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const mergedCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, contextConfig.className, className, rootClassName, hashId, cssVarCls, {
        [`${prefixCls}-rtl`]: direction === 'rtl',
        [`${prefixCls}-disabled`]: disabled
    });
    const actionBtnCls = `${prefixCls}-actions-btn`;
    const actionListCls = `${prefixCls}-actions-list`;
    // ============================ Value =============================
    const [innerValue, setInnerValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMergedState$3e$__["useMergedState"])(defaultValue || '', {
        value
    });
    const triggerValueChange = (nextValue, event)=>{
        setInnerValue(nextValue);
        if (onChange) {
            onChange(nextValue, event);
        }
    };
    // ============================ Speech ============================
    const [speechPermission, triggerSpeech, speechRecording] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$useSpeech$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "ForwardSender.useSpeech": (transcript)=>{
            triggerValueChange(`${innerValue} ${transcript}`);
        }
    }["ForwardSender.useSpeech"], allowSpeech);
    // ========================== Components ==========================
    const InputTextArea = getComponent(components, [
        'input'
    ], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Input$3e$__["Input"].TextArea);
    const domProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(rest, {
        attr: true,
        aria: true,
        data: true
    });
    const inputProps = {
        ...domProps,
        ref: inputRef
    };
    // ============================ Events ============================
    const triggerSend = ()=>{
        if (innerValue && onSubmit && !loading) {
            onSubmit(innerValue);
        }
    };
    const triggerClear = ()=>{
        triggerValueChange('');
    };
    // ============================ Submit ============================
    const isCompositionRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(false);
    const onInternalCompositionStart = ()=>{
        isCompositionRef.current = true;
    };
    const onInternalCompositionEnd = ()=>{
        isCompositionRef.current = false;
    };
    const onInternalKeyPress = (e)=>{
        const canSubmit = e.key === 'Enter' && !isCompositionRef.current;
        // Check for `submitType` to submit
        switch(submitType){
            case 'enter':
                if (canSubmit && !e.shiftKey) {
                    e.preventDefault();
                    triggerSend();
                }
                break;
            case 'shiftEnter':
                if (canSubmit && e.shiftKey) {
                    e.preventDefault();
                    triggerSend();
                }
                break;
        }
        onKeyPress?.(e);
    };
    // ============================ Paste =============================
    const onInternalPaste = (e)=>{
        // Get files
        const files = e.clipboardData?.files;
        if (files?.length && onPasteFile) {
            onPasteFile(files[0], files);
            e.preventDefault();
        }
        onPaste?.(e);
    };
    // ============================ Focus =============================
    const onContentMouseDown = (e)=>{
        // If input focused but click on the container,
        // input will lose focus.
        // We call `preventDefault` to prevent this behavior
        if (e.target !== containerRef.current?.querySelector(`.${inputCls}`)) {
            e.preventDefault();
        }
        inputRef.current?.focus();
    };
    // ============================ Action ============================
    let actionNode = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$flex$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Flex$3e$__["Flex"], {
        className: `${actionListCls}-presets`
    }, allowSpeech && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$SpeechButton$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null), loading ? /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$LoadingButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null) : /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$SendButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null));
    // Custom actions
    if (typeof actions === 'function') {
        actionNode = actions(actionNode, {
            components: sharedRenderComponents
        });
    } else if (actions || actions === false) {
        actionNode = actions;
    }
    // Custom actions context props
    const actionsButtonContextProps = {
        prefixCls: actionBtnCls,
        onSend: triggerSend,
        onSendDisabled: !innerValue,
        onClear: triggerClear,
        onClearDisabled: !innerValue,
        onCancel,
        onCancelDisabled: !loading,
        onSpeech: ()=>triggerSpeech(false),
        onSpeechDisabled: !speechPermission,
        speechRecording,
        disabled
    };
    // ============================ Footer ============================
    const footerNode = typeof footer === 'function' ? footer({
        components: sharedRenderComponents
    }) : footer || null;
    // ============================ Render ============================
    return wrapCSSVar(/*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        ref: containerRef,
        className: mergedCls,
        style: {
            ...contextConfig.style,
            ...style
        }
    }, header && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$SenderHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SendHeaderContext"].Provider, {
        value: {
            prefixCls
        }
    }, header), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$components$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ActionButtonContext"].Provider, {
        value: actionsButtonContextProps
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: `${prefixCls}-content`,
        onMouseDown: onContentMouseDown
    }, prefix && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-prefix`, contextConfig.classNames.prefix, classNames.prefix),
        style: {
            ...contextConfig.styles.prefix,
            ...styles.prefix
        }
    }, prefix), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(InputTextArea, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, inputProps, {
        disabled: disabled,
        style: {
            ...contextConfig.styles.input,
            ...styles.input
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(inputCls, contextConfig.classNames.input, classNames.input),
        autoSize: autoSize,
        value: innerValue,
        onChange: (event)=>{
            triggerValueChange(event.target.value, event);
            triggerSpeech(true);
        },
        onPressEnter: onInternalKeyPress,
        onCompositionStart: onInternalCompositionStart,
        onCompositionEnd: onInternalCompositionEnd,
        onKeyDown: onKeyDown,
        onPaste: onInternalPaste,
        variant: "borderless",
        readOnly: readOnly
    })), actionNode && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(actionListCls, contextConfig.classNames.actions, classNames.actions),
        style: {
            ...contextConfig.styles.actions,
            ...styles.actions
        }
    }, actionNode)), footerNode && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-footer`, contextConfig.classNames.footer, classNames.footer),
        style: {
            ...contextConfig.styles.footer,
            ...styles.footer
        }
    }, footerNode))));
});
const Sender = ForwardSender;
if ("TURBOPACK compile-time truthy", 1) {
    Sender.displayName = 'Sender';
}
Sender.Header = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$SenderHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const __TURBOPACK__default__export__ = Sender;
}}),
"[project]/node_modules/@ant-design/x/es/sender/index.js [app-client] (ecmascript) <export default as Sender>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Sender": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$x$2f$es$2f$sender$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/x/es/sender/index.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=node_modules_%40ant-design_x_es_53cdc150._.js.map