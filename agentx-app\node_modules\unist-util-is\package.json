{"name": "unist-util-is", "version": "6.0.0", "description": "unist utility to check if a node passes a test", "license": "MIT", "keywords": ["unist", "unist-util", "util", "utility", "tree", "node", "is", "equal", "check", "test", "type"], "repository": "syntax-tree/unist-util-is", "bugs": "https://github.com/syntax-tree/unist-util-is/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://github.com/Roang-zero1)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^3.0.0"}, "devDependencies": {"@types/mdast": "^4.0.0", "@types/node": "^20.0.0", "c8": "^8.0.0", "prettier": "^2.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "tsd": "^0.28.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.54.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && tsd && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "#": "needed `any`s", "ignoreFiles": ["lib/index.d.ts"], "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-unnecessary-type-arguments": "off", "import/no-extraneous-dependencies": "off"}}], "prettier": true}}