/* [project]/src/styles/markdown.css [app-client] (css) */
.markdown-content {
  color: #24292e;
  line-height: 1.6;
}

.markdown-content p code, .markdown-content li code, .markdown-content h1 code, .markdown-content h2 code, .markdown-content h3 code, .markdown-content h4 code, .markdown-content h5 code, .markdown-content h6 code {
  color: #24292e !important;
  white-space: nowrap !important;
  vertical-align: baseline !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  background-color: #f6f8fa !important;
  border: 1px solid #e1e4e8 !important;
  border-radius: 4px !important;
  width: auto !important;
  max-width: none !important;
  padding: 2px 6px !important;
  font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, Liberation Mono, Menlo, monospace !important;
  font-size: .9em !important;
  display: inline-block !important;
}

.markdown-content pre {
  white-space: pre !important;
  color: #24292e !important;
  background-color: #f6f8fa !important;
  border: 1px solid #e1e4e8 !important;
  border-radius: 6px !important;
  margin: 16px 0 !important;
  padding: 16px !important;
  font-family: <PERSON><PERSON>ono-<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Mono, <PERSON>lo, monospace !important;
  font-size: 14px !important;
  line-height: 1.45 !important;
  overflow: auto !important;
}

.markdown-content pre code {
  font-size: inherit !important;
  color: inherit !important;
  white-space: pre !important;
  word-break: normal !important;
  overflow-wrap: normal !important;
  background: none !important;
  border: none !important;
  border-radius: 0 !important;
  padding: 0 !important;
  display: block !important;
}

.markdown-content p {
  color: #24292e !important;
  margin: 0 0 12px !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
}

.markdown-content h1 {
  color: #24292e !important;
  margin: 0 0 16px !important;
  font-size: 20px !important;
  font-weight: 600 !important;
}

.markdown-content h2 {
  color: #24292e !important;
  margin: 0 0 12px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}

.markdown-content h3 {
  color: #24292e !important;
  margin: 0 0 12px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}

.markdown-content ul, .markdown-content ol {
  color: #24292e !important;
  margin: 0 0 12px !important;
  padding-left: 20px !important;
  font-size: 14px !important;
}


/*# sourceMappingURL=src_styles_markdown_1aafa561.css.map*/