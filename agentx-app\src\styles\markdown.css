/* Markdown 样式 */
.markdown-content {
  line-height: 1.6;
  color: #24292e;
}

.markdown-content code {
  background-color: #f6f8fa !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 0.9em !important;
  font-family: <PERSON><PERSON><PERSON>-<PERSON>, <PERSON><PERSON><PERSON>, "Liberation Mono", Menlo, monospace !important;
  border: 1px solid #e1e4e8 !important;
  color: #24292e !important;
  white-space: nowrap !important;
  display: inline-block !important;
  vertical-align: baseline !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  max-width: none !important;
  width: auto !important;
}

.markdown-content pre {
  background-color: #f6f8fa !important;
  padding: 16px !important;
  border-radius: 6px !important;
  overflow: auto !important;
  font-size: 14px !important;
  line-height: 1.45 !important;
  margin: 16px 0 !important;
  border: 1px solid #e1e4e8 !important;
  font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, monospace !important;
  white-space: pre !important;
  color: #24292e !important;
}

.markdown-content p {
  margin: 0 0 12px 0 !important;
  line-height: 1.6 !important;
  color: #24292e !important;
  font-size: 14px !important;
}

.markdown-content h1 {
  font-size: 20px !important;
  font-weight: 600 !important;
  margin: 0 0 16px 0 !important;
  color: #24292e !important;
}

.markdown-content h2 {
  font-size: 18px !important;
  font-weight: 600 !important;
  margin: 0 0 12px 0 !important;
  color: #24292e !important;
}

.markdown-content h3 {
  font-size: 16px !important;
  font-weight: 600 !important;
  margin: 0 0 12px 0 !important;
  color: #24292e !important;
}

.markdown-content ul,
.markdown-content ol {
  margin: 0 0 12px 0 !important;
  padding-left: 20px !important;
  color: #24292e !important;
  font-size: 14px !important;
}
