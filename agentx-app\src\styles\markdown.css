/* Markdown 样式 */
.markdown-content {
  line-height: 1.6;
  color: #24292e;
}

/* 行内代码样式 - 只对不在 pre 标签内的 code 应用 */
.markdown-content p code,
.markdown-content li code,
.markdown-content h1 code,
.markdown-content h2 code,
.markdown-content h3 code,
.markdown-content h4 code,
.markdown-content h5 code,
.markdown-content h6 code {
  background-color: #f6f8fa !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 0.9em !important;
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace !important;
  border: 1px solid #e1e4e8 !important;
  color: #24292e !important;
  white-space: nowrap !important;
  display: inline-block !important;
  vertical-align: baseline !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  max-width: none !important;
  width: auto !important;
}

/* 代码块样式 */
.markdown-content pre {
  background-color: #f6f8fa !important;
  padding: 16px !important;
  border-radius: 6px !important;
  overflow: auto !important;
  font-size: 14px !important;
  line-height: 1.45 !important;
  margin: 16px 0 !important;
  border: 1px solid #e1e4e8 !important;
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace !important;
  white-space: pre !important;
  color: #24292e !important;
}

/* 代码块内的 code 标签 */
.markdown-content pre code {
  background: none !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  font-size: inherit !important;
  color: inherit !important;
  white-space: pre !important;
  display: block !important;
  word-break: normal !important;
  overflow-wrap: normal !important;
}

.markdown-content p {
  margin: 0 0 12px 0 !important;
  line-height: 1.6 !important;
  color: #24292e !important;
  font-size: 14px !important;
}

.markdown-content h1 {
  font-size: 20px !important;
  font-weight: 600 !important;
  margin: 0 0 16px 0 !important;
  color: #24292e !important;
}

.markdown-content h2 {
  font-size: 18px !important;
  font-weight: 600 !important;
  margin: 0 0 12px 0 !important;
  color: #24292e !important;
}

.markdown-content h3 {
  font-size: 16px !important;
  font-weight: 600 !important;
  margin: 0 0 12px 0 !important;
  color: #24292e !important;
}

.markdown-content ul,
.markdown-content ol {
  margin: 0 0 12px 0 !important;
  padding-left: 20px !important;
  color: #24292e !important;
  font-size: 14px !important;
}